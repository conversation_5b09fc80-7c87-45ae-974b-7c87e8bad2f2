package ai.zencoder.plugin.observers.auth;

import ai.zencoder.plugin.auth.AuthInfo;
import ai.zencoder.plugin.auth.UserData;
import com.intellij.openapi.diagnostic.Logger;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.lang.reflect.Method;

/**
 * AuthObserver 工具类
 * 解决类加载器冲突导致的 ClassCastException 问题
 */
public class AuthObserverUtils {
    
    private static final Logger LOG = Logger.getInstance(AuthObserverUtils.class);
    
    /**
     * 安全地触发登出事件
     * 使用反射来避免类加载器冲突
     * 
     * @param observer 观察者对象（可能是任何类型）
     */
    public static void safeSignOut(@Nullable Object observer) {
        if (observer == null) {
            LOG.warn("Observer is null, cannot trigger sign out");
            return;
        }
        
        try {
            // 首先尝试直接类型转换
            if (observer instanceof AuthObserver) {
                ((AuthObserver) observer).onSignOut();
                LOG.info("Successfully triggered sign out via direct cast");
                return;
            }
        } catch (ClassCastException e) {
            LOG.warn("Direct cast failed, attempting reflection approach", e);
        }
        
        // 如果直接转换失败，使用反射
        try {
            Class<?> observerClass = observer.getClass();
            Method signOutMethod = findMethod(observerClass, "onSignOut");
            
            if (signOutMethod != null) {
                signOutMethod.setAccessible(true);
                signOutMethod.invoke(observer);
                LOG.info("Successfully triggered sign out via reflection");
            } else {
                LOG.error("onSignOut method not found in class: " + observerClass.getName());
            }
        } catch (Exception e) {
            LOG.error("Failed to trigger sign out via reflection", e);
        }
    }
    
    /**
     * 安全地触发认证成功事件
     * 
     * @param observer 观察者对象
     * @param authInfo 认证信息
     */
    public static void safeAuthSuccess(@Nullable Object observer, @NotNull AuthInfo authInfo) {
        if (observer == null) {
            LOG.warn("Observer is null, cannot trigger auth success");
            return;
        }
        
        try {
            if (observer instanceof AuthObserver) {
                ((AuthObserver) observer).onAuthenticationSuccess(authInfo);
                return;
            }
        } catch (ClassCastException e) {
            LOG.warn("Direct cast failed for auth success, attempting reflection", e);
        }
        
        try {
            Class<?> observerClass = observer.getClass();
            Method method = findMethod(observerClass, "onAuthenticationSuccess", AuthInfo.class);
            
            if (method != null) {
                method.setAccessible(true);
                method.invoke(observer, authInfo);
                LOG.info("Successfully triggered auth success via reflection");
            }
        } catch (Exception e) {
            LOG.error("Failed to trigger auth success via reflection", e);
        }
    }
    
    /**
     * 安全地触发认证重置事件
     * 
     * @param observer 观察者对象
     */
    public static void safeAuthReset(@Nullable Object observer) {
        if (observer == null) {
            LOG.warn("Observer is null, cannot trigger auth reset");
            return;
        }
        
        try {
            if (observer instanceof AuthObserver) {
                ((AuthObserver) observer).onAuthenticationReset();
                return;
            }
        } catch (ClassCastException e) {
            LOG.warn("Direct cast failed for auth reset, attempting reflection", e);
        }
        
        try {
            Class<?> observerClass = observer.getClass();
            Method method = findMethod(observerClass, "onAuthenticationReset");
            
            if (method != null) {
                method.setAccessible(true);
                method.invoke(observer);
                LOG.info("Successfully triggered auth reset via reflection");
            }
        } catch (Exception e) {
            LOG.error("Failed to trigger auth reset via reflection", e);
        }
    }
    
    /**
     * 查找指定名称和参数类型的方法
     * 
     * @param clazz 类
     * @param methodName 方法名
     * @param parameterTypes 参数类型
     * @return 找到的方法，如果没找到返回null
     */
    private static Method findMethod(@NotNull Class<?> clazz, @NotNull String methodName, Class<?>... parameterTypes) {
        try {
            return clazz.getMethod(methodName, parameterTypes);
        } catch (NoSuchMethodException e) {
            // 尝试在父类和接口中查找
            Class<?> superClass = clazz.getSuperclass();
            if (superClass != null) {
                Method method = findMethod(superClass, methodName, parameterTypes);
                if (method != null) {
                    return method;
                }
            }
            
            // 在接口中查找
            for (Class<?> iface : clazz.getInterfaces()) {
                Method method = findMethod(iface, methodName, parameterTypes);
                if (method != null) {
                    return method;
                }
            }
            
            LOG.debug("Method not found: " + methodName + " in class: " + clazz.getName());
            return null;
        }
    }
    
    /**
     * 检查对象是否实现了 AuthObserver 接口（通过方法检查而非类型检查）
     * 
     * @param obj 要检查的对象
     * @return 如果对象具有 AuthObserver 的方法则返回true
     */
    public static boolean isAuthObserver(@Nullable Object obj) {
        if (obj == null) {
            return false;
        }
        
        try {
            // 检查是否有 onSignOut 方法
            Method signOutMethod = findMethod(obj.getClass(), "onSignOut");
            return signOutMethod != null;
        } catch (Exception e) {
            LOG.debug("Error checking if object is AuthObserver", e);
            return false;
        }
    }
    
    /**
     * 获取对象的类加载器信息（用于调试）
     * 
     * @param obj 对象
     * @return 类加载器信息字符串
     */
    public static String getClassLoaderInfo(@Nullable Object obj) {
        if (obj == null) {
            return "null object";
        }
        
        Class<?> clazz = obj.getClass();
        ClassLoader classLoader = clazz.getClassLoader();
        
        return String.format("Class: %s, ClassLoader: %s", 
                           clazz.getName(), 
                           classLoader != null ? classLoader.toString() : "null");
    }
}
