package ai.zencoder.plugin.observers.auth;

import ai.zencoder.plugin.auth.AuthInfo;
import ai.zencoder.plugin.auth.UserData;
import com.intellij.openapi.diagnostic.Logger;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/**
 * 认证观察者接口
 * 用于监听认证状态变化
 *
 * 注意：为了避免类加载器冲突，建议使用 AuthObserverUtils 进行安全的方法调用
 */
public interface AuthObserver {

    Logger LOG = Logger.getInstance(AuthObserver.class);

    /**
     * 当认证成功时调用
     * @param authInfo 认证信息
     */
    void onAuthenticationSuccess(@NotNull AuthInfo authInfo);

    /**
     * 当认证失败时调用
     * @param error 错误信息
     */
    void onAuthenticationFailure(@NotNull String error);

    /**
     * 当用户数据更新时调用
     * @param userData 用户数据
     */
    void onUserDataUpdated(@Nullable UserData userData);

    /**
     * 当认证状态重置时调用
     */
    void onAuthenticationReset();

    /**
     * 当令牌刷新时调用
     * @param newAuthInfo 新的认证信息
     */
    void onTokenRefreshed(@NotNull AuthInfo newAuthInfo);

    /**
     * 当用户登出时调用
     */
    void onSignOut();

    /**
     * 简化的认证成功回调（兼容现有代码）
     * @param authInfo 认证信息
     */
    default void a(@NotNull AuthInfo authInfo) {
        onAuthenticationSuccess(authInfo);
    }

    /**
     * 安全的类型转换方法
     * 用于避免 ClassCastException
     *
     * @param obj 要转换的对象
     * @return 如果转换成功返回 AuthObserver，否则返回 null
     */
    static AuthObserver safeCast(@Nullable Object obj) {
        if (obj == null) {
            return null;
        }

        try {
            if (obj instanceof AuthObserver) {
                return (AuthObserver) obj;
            }
        } catch (ClassCastException e) {
            LOG.warn("ClassCastException when casting to AuthObserver: " +
                    AuthObserverUtils.getClassLoaderInfo(obj), e);
        }

        return null;
    }

    /**
     * 安全地调用 onSignOut 方法
     *
     * @param observer 观察者对象
     */
    static void safeSignOut(@Nullable Object observer) {
        AuthObserverUtils.safeSignOut(observer);
    }
}
