package ai.zencoder.plugin.observers.auth;

import ai.zencoder.plugin.auth.AuthInfo;
import ai.zencoder.plugin.auth.UserData;
import com.intellij.openapi.diagnostic.Logger;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.concurrent.CopyOnWriteArrayList;
import java.util.function.Consumer;

/**
 * 认证观察者的默认实现
 * 支持多个监听器的注册和通知
 */
public class AuthObserverImpl implements AuthObserver {
    
    private static final Logger LOG = Logger.getInstance(AuthObserverImpl.class);
    
    private final CopyOnWriteArrayList<Consumer<AuthInfo>> authSuccessListeners = new CopyOnWriteArrayList<>();
    private final CopyOnWriteArrayList<Consumer<String>> authFailureListeners = new CopyOnWriteArrayList<>();
    private final CopyOnWriteArrayList<Consumer<UserData>> userDataListeners = new CopyOnWriteArrayList<>();
    private final CopyOnWriteArrayList<Runnable> resetListeners = new CopyOnWriteArrayList<>();
    private final CopyOnWriteArrayList<Consumer<AuthInfo>> tokenRefreshListeners = new CopyOnWriteArrayList<>();
    private final CopyOnWriteArrayList<Runnable> signOutListeners = new CopyOnWriteArrayList<>();
    
    @Override
    public void onAuthenticationSuccess(@NotNull AuthInfo authInfo) {
        LOG.info("Authentication successful: " + authInfo);
        authSuccessListeners.forEach(listener -> {
            try {
                listener.accept(authInfo);
            } catch (Exception e) {
                LOG.error("Error in authentication success listener", e);
            }
        });
    }
    
    @Override
    public void onAuthenticationFailure(@NotNull String error) {
        LOG.warn("Authentication failed: " + error);
        authFailureListeners.forEach(listener -> {
            try {
                listener.accept(error);
            } catch (Exception e) {
                LOG.error("Error in authentication failure listener", e);
            }
        });
    }
    
    @Override
    public void onUserDataUpdated(@Nullable UserData userData) {
        LOG.info("User data updated: " + userData);
        userDataListeners.forEach(listener -> {
            try {
                listener.accept(userData);
            } catch (Exception e) {
                LOG.error("Error in user data listener", e);
            }
        });
    }
    
    @Override
    public void onAuthenticationReset() {
        LOG.info("Authentication reset");
        resetListeners.forEach(listener -> {
            try {
                listener.run();
            } catch (Exception e) {
                LOG.error("Error in authentication reset listener", e);
            }
        });
    }
    
    @Override
    public void onTokenRefreshed(@NotNull AuthInfo newAuthInfo) {
        LOG.info("Token refreshed: " + newAuthInfo);
        tokenRefreshListeners.forEach(listener -> {
            try {
                listener.accept(newAuthInfo);
            } catch (Exception e) {
                LOG.error("Error in token refresh listener", e);
            }
        });
    }
    
    @Override
    public void onSignOut() {
        LOG.info("User signed out");
        signOutListeners.forEach(listener -> {
            try {
                listener.run();
            } catch (Exception e) {
                LOG.error("Error in sign out listener", e);
            }
        });
    }
    
    // 监听器注册方法
    
    public void addAuthSuccessListener(@NotNull Consumer<AuthInfo> listener) {
        authSuccessListeners.add(listener);
    }
    
    public void removeAuthSuccessListener(@NotNull Consumer<AuthInfo> listener) {
        authSuccessListeners.remove(listener);
    }
    
    public void addAuthFailureListener(@NotNull Consumer<String> listener) {
        authFailureListeners.add(listener);
    }
    
    public void removeAuthFailureListener(@NotNull Consumer<String> listener) {
        authFailureListeners.remove(listener);
    }
    
    public void addUserDataListener(@NotNull Consumer<UserData> listener) {
        userDataListeners.add(listener);
    }
    
    public void removeUserDataListener(@NotNull Consumer<UserData> listener) {
        userDataListeners.remove(listener);
    }
    
    public void addResetListener(@NotNull Runnable listener) {
        resetListeners.add(listener);
    }
    
    public void removeResetListener(@NotNull Runnable listener) {
        resetListeners.remove(listener);
    }
    
    public void addTokenRefreshListener(@NotNull Consumer<AuthInfo> listener) {
        tokenRefreshListeners.add(listener);
    }
    
    public void removeTokenRefreshListener(@NotNull Consumer<AuthInfo> listener) {
        tokenRefreshListeners.remove(listener);
    }
    
    public void addSignOutListener(@NotNull Runnable listener) {
        signOutListeners.add(listener);
    }
    
    public void removeSignOutListener(@NotNull Runnable listener) {
        signOutListeners.remove(listener);
    }
    
    /**
     * 清除所有监听器
     */
    public void clearAllListeners() {
        authSuccessListeners.clear();
        authFailureListeners.clear();
        userDataListeners.clear();
        resetListeners.clear();
        tokenRefreshListeners.clear();
        signOutListeners.clear();
    }
}
