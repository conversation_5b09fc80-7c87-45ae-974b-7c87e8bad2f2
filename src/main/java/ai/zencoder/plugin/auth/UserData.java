package ai.zencoder.plugin.auth;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 用户数据类
 * 包含用户的基本信息、计划和权限
 */
public class UserData {
    
    @NotNull
    private final String userId;
    
    @NotNull
    private final String displayName;
    
    @NotNull
    private final String email;
    
    @NotNull
    private final List<String> plans;
    
    @NotNull
    private final List<String> permissions;
    
    @Nullable
    private final Map<String, Object> customClaims;
    
    public UserData(@NotNull String userId, 
                   @NotNull String displayName, 
                   @NotNull String email,
                   @NotNull List<String> plans,
                   @NotNull List<String> permissions,
                   @Nullable Map<String, Object> customClaims) {
        this.userId = Objects.requireNonNull(userId, "userId cannot be null");
        this.displayName = Objects.requireNonNull(displayName, "displayName cannot be null");
        this.email = Objects.requireNonNull(email, "email cannot be null");
        this.plans = Objects.requireNonNull(plans, "plans cannot be null");
        this.permissions = Objects.requireNonNull(permissions, "permissions cannot be null");
        this.customClaims = customClaims;
    }
    
    @NotNull
    public String getUserId() {
        return userId;
    }
    
    @NotNull
    public String getDisplayName() {
        return displayName;
    }
    
    @NotNull
    public String getEmail() {
        return email;
    }
    
    @NotNull
    public List<String> getPlans() {
        return plans;
    }
    
    @NotNull
    public List<String> getPermissions() {
        return permissions;
    }
    
    @Nullable
    public Map<String, Object> getCustomClaims() {
        return customClaims;
    }
    
    /**
     * 检查用户是否有指定的计划
     * @param plan 计划名称
     * @return true如果用户有该计划，false否则
     */
    public boolean hasPlan(@NotNull String plan) {
        return plans.contains(plan);
    }
    
    /**
     * 检查用户是否有指定的权限
     * @param permission 权限名称
     * @return true如果用户有该权限，false否则
     */
    public boolean hasPermission(@NotNull String permission) {
        return permissions.contains(permission);
    }
    
    /**
     * 检查用户是否是VIP用户
     * @return true如果用户有任何付费计划，false否则
     */
    public boolean isVip() {
        return plans.stream().anyMatch(plan -> 
            !plan.equalsIgnoreCase("free") && 
            !plan.equalsIgnoreCase("trial")
        );
    }
    
    /**
     * 获取自定义声明的值
     * @param key 声明键
     * @return 声明值，如果不存在则返回null
     */
    @Nullable
    public Object getCustomClaim(@NotNull String key) {
        return customClaims != null ? customClaims.get(key) : null;
    }
    
    @Override
    public String toString() {
        return "UserData{" +
                "userId='" + userId + '\'' +
                ", displayName='" + displayName + '\'' +
                ", email='" + email + '\'' +
                ", plans=" + plans +
                ", permissions=" + permissions +
                ", customClaims=" + (customClaims != null ? customClaims.size() + " claims" : "null") +
                '}';
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        UserData userData = (UserData) o;
        
        if (!userId.equals(userData.userId)) return false;
        if (!displayName.equals(userData.displayName)) return false;
        if (!email.equals(userData.email)) return false;
        if (!plans.equals(userData.plans)) return false;
        if (!permissions.equals(userData.permissions)) return false;
        return Objects.equals(customClaims, userData.customClaims);
    }
    
    @Override
    public int hashCode() {
        int result = userId.hashCode();
        result = 31 * result + displayName.hashCode();
        result = 31 * result + email.hashCode();
        result = 31 * result + plans.hashCode();
        result = 31 * result + permissions.hashCode();
        result = 31 * result + (customClaims != null ? customClaims.hashCode() : 0);
        return result;
    }
}
