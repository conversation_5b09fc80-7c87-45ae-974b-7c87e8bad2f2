package ai.zencoder.plugin.action;

import ai.zencoder.plugin.bypass.*;
import ai.zencoder.plugin.noauth.AuthServiceReplacer;
import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.ui.Messages;
import org.jetbrains.annotations.NotNull;

/**
 * 切换免认证模式的操作
 */
public class ToggleNoAuthAction extends AnAction {
    
    private static final Logger LOG = Logger.getInstance(ToggleNoAuthAction.class);
    
    @Override
    public void actionPerformed(@NotNull AnActionEvent e) {
        try {
            // Check comprehensive bypass system status
            boolean bypassActive = ZencoderStateSynchronizer.isAuthBypassActive();
            boolean hijackActive = ZencoderServiceHijacker.isHijackActive();
            boolean injectionActive = ZencoderCredentialInjector.isInjectionActive();
            boolean monitoringActive = ZencoderStateSynchronizer.isMonitoringActive();
            
            String title = "Zencoder Bypass System Status";
            StringBuilder message = new StringBuilder();
            
            if (bypassActive && hijackActive && injectionActive) {
                message.append("🟢 Zencoder Bypass System is FULLY ACTIVE\n\n");
                
                // Show user info
                var userData = ZencoderCredentialInjector.getCurrentUserData();
                if (userData != null) {
                    message.append("👤 Bypass User: ").append(userData.getDisplayName()).append("\n");
                    message.append("📧 Email: ").append(userData.getEmail()).append("\n");
                    message.append("💎 VIP Status: ").append(userData.isVip() ? "ACTIVE" : "INACTIVE").append("\n");
                    message.append("📋 Plans: ").append(userData.getPlans()).append("\n");
                    message.append("🔑 Permissions: ").append(userData.getPermissions().size()).append(" granted\n\n");
                }
                
                message.append("🔧 System Components:\n");
                message.append("• Service Hijacking: ✅ ACTIVE\n");
                message.append("• Credential Injection: ✅ ACTIVE\n");
                message.append("• State Monitoring: ").append(monitoringActive ? "✅ ACTIVE" : "❌ INACTIVE").append("\n");
                message.append("• Reflection Cache: ").append(ZencoderReflectionUtils.getCacheStats()).append("\n\n");
                
                // Show detected Zencoder info
                String zencoderPluginId = ZencoderTargetDiscovery.getDetectedZencoderPluginId();
                if (zencoderPluginId != null) {
                    message.append("🎯 Target Plugin: ").append(zencoderPluginId).append("\n");
                }
                
                message.append("\n⚠️ Note: This is for development use only.");
                
            } else {
                message.append("🔴 Zencoder Bypass System is PARTIALLY ACTIVE or INACTIVE\n\n");
                
                message.append("🔧 Component Status:\n");
                message.append("• Authentication Bypass: ").append(bypassActive ? "✅ ACTIVE" : "❌ INACTIVE").append("\n");
                message.append("• Service Hijacking: ").append(hijackActive ? "✅ ACTIVE" : "❌ INACTIVE").append("\n");
                message.append("• Credential Injection: ").append(injectionActive ? "✅ ACTIVE" : "❌ INACTIVE").append("\n");
                message.append("• State Monitoring: ").append(monitoringActive ? "✅ ACTIVE" : "❌ INACTIVE").append("\n\n");
                
                // Check legacy system
                AuthServiceReplacer replacer = ApplicationManager.getApplication().getService(AuthServiceReplacer.class);
                if (replacer != null && replacer.isNoAuthActive()) {
                    message.append("🔄 Legacy No-Auth System: ✅ ACTIVE\n");
                    message.append("• Current AuthService: ").append(replacer.getAuthServiceClassName()).append("\n\n");
                }
                
                message.append("💡 To activate the full bypass system:\n");
                message.append("1. Ensure Zencoder official plugin is installed\n");
                message.append("2. Restart the IDE or force re-initialization\n");
                message.append("3. Check logs for detailed error information");
            }
            
            Messages.showInfoMessage(e.getProject(), message.toString(), title);
            
        } catch (Exception ex) {
            LOG.error("Failed to check bypass system status", ex);
            Messages.showErrorDialog(e.getProject(), 
                "Failed to check bypass system status: " + ex.getMessage(), 
                "Error");
        }
    }
    
    @Override
    public void update(@NotNull AnActionEvent e) {
        // 动态更新操作文本
        try {
            boolean bypassActive = ZencoderStateSynchronizer.isAuthBypassActive();
            boolean hijackActive = ZencoderServiceHijacker.isHijackActive();
            boolean injectionActive = ZencoderCredentialInjector.isInjectionActive();

            if (bypassActive && hijackActive && injectionActive) {
                e.getPresentation().setText("🟢 Zencoder Bypass: FULLY ACTIVE");
                e.getPresentation().setDescription("Zencoder bypass system is fully operational");
            } else if (bypassActive || hijackActive || injectionActive) {
                e.getPresentation().setText("🟡 Zencoder Bypass: PARTIAL");
                e.getPresentation().setDescription("Zencoder bypass system is partially active");
            } else {
                // Check legacy system
                AuthServiceReplacer replacer = ApplicationManager.getApplication().getService(AuthServiceReplacer.class);
                if (replacer != null && replacer.isNoAuthActive()) {
                    e.getPresentation().setText("🔄 Legacy No-Auth: ACTIVE");
                    e.getPresentation().setDescription("Legacy No-Auth mode is active");
                } else {
                    e.getPresentation().setText("🔴 Bypass: INACTIVE");
                    e.getPresentation().setDescription("Zencoder bypass system is inactive");
                }
            }

        } catch (Exception ex) {
            e.getPresentation().setText("❌ Bypass: ERROR");
            e.getPresentation().setDescription("Error checking bypass status: " + ex.getMessage());
        }
    }
}
