package ai.zencoder.plugin.api;

import ai.zencoder.plugin.auth.AuthInfo;
import ai.zencoder.plugin.auth.AuthService;
import ai.zencoder.plugin.auth.UserData;
import ai.zencoder.plugin.auth.NoAuthInfoException;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import java.util.Collections;

/**
 * 免认证的AuthService实现
 * 提供模拟的认证信息，绕过真实的OAuth认证流程
 */
public class NoAuthService implements AuthService {
    
    // 模拟的访问令牌
    private static final String MOCK_ACCESS_TOKEN = "mock_access_token_" + System.currentTimeMillis();
    
    // 模拟的刷新令牌
    private static final String MOCK_REFRESH_TOKEN = "mock_refresh_token_" + System.currentTimeMillis();
    
    // 模拟的用户数据
    private static final UserData MOCK_USER_DATA = new UserData(
        "mock_user_id",
        "Mock User", 
        "<EMAIL>",
        Collections.singletonList("Free"), // 计划列表
        Collections.emptyList(), // 权限列表
        null // 自定义声明
    );
    
    // 模拟的认证信息
    private final AuthInfo mockAuthInfo = new AuthInfo(MOCK_ACCESS_TOKEN, MOCK_REFRESH_TOKEN);

    @NotNull
    @Override
    public String h() throws NoAuthInfoException {
        return MOCK_ACCESS_TOKEN;
    }

    @NotNull
    @Override
    public AuthInfo b() throws NoAuthInfoException {
        return mockAuthInfo;
    }

    @Nullable
    @Override
    public AuthInfo a() {
        return mockAuthInfo;
    }

    @Nullable
    @Override
    public UserData getUserData() {
        return MOCK_USER_DATA;
    }

    @Override
    public void signIn() {
        // 模拟登录 - 不执行任何操作
        System.out.println("NoAuthService: Mock sign in called");
    }

    @Override
    public void signUp() {
        // 模拟注册 - 不执行任何操作
        System.out.println("NoAuthService: Mock sign up called");
    }

    @Override
    public boolean isAuthenticated() {
        // 始终返回已认证状态
        return true;
    }

    @Override
    public void resetAuthentication() {
        // 模拟重置认证 - 不执行任何操作
        System.out.println("NoAuthService: Mock reset authentication called");
    }

    @NotNull
    @Override
    public AuthInfo refreshAuthentication(@NotNull String expiredAccessToken) {
        // 模拟刷新认证 - 返回相同的认证信息
        System.out.println("NoAuthService: Mock refresh authentication called with token: " + expiredAccessToken);
        return mockAuthInfo;
    }

    @Override
    public void signOut() {
        // 模拟登出 - 不执行任何操作
        System.out.println("NoAuthService: Mock sign out called");
    }

    @Override
    public boolean isTokenValid(@NotNull String token) {
        // 模拟令牌验证 - 始终返回有效
        return true;
    }
}
