package ai.zencoder.plugin.api;

import ai.zencoder.plugin.auth.AuthInfo;
import ai.zencoder.plugin.auth.AuthService;
import ai.zencoder.plugin.auth.UserData;
import ai.zencoder.plugin.auth.NoAuthInfoException;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 安全的免认证AuthService实现
 * 提供受限的模拟认证功能，包含安全检查和审计日志
 */
public class SecureNoAuthService implements AuthService {
    
    private static final Logger LOG = Logger.getInstance(SecureNoAuthService.class);
    
    // 安全配置
    private static final int MAX_OPERATIONS_PER_SESSION = 1000;
    private static final Set<String> ALLOWED_ENVIRONMENTS = new HashSet<>(Arrays.asList(
        "development", "test", "local", "debug"
    ));
    
    // 操作计数器
    private final AtomicInteger operationCount = new AtomicInteger(0);
    
    // 会话信息
    private final String sessionId;
    private final LocalDateTime sessionStart;
    
    // 模拟的认证信息（带有限制）
    private final AuthInfo restrictedAuthInfo;
    private final UserData restrictedUserData;
    
    public SecureNoAuthService() {
        // 环境安全检查
        validateEnvironment();
        
        this.sessionId = generateSessionId();
        this.sessionStart = LocalDateTime.now();
        
        // 创建受限的认证信息
        this.restrictedAuthInfo = createRestrictedAuthInfo();
        this.restrictedUserData = createRestrictedUserData();
        
        LOG.warn("SecureNoAuthService initialized - Session: " + sessionId + 
                " at " + sessionStart.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        
        // 记录安全警告
        logSecurityWarning();
    }
    
    /**
     * 验证运行环境是否安全
     */
    private void validateEnvironment() {
        // 检查是否在测试模式
        try {
            if (ApplicationManager.getApplication() != null &&
                ApplicationManager.getApplication().isUnitTestMode()) {
                return; // 测试模式允许
            }
        } catch (Exception e) {
            // 在单元测试环境中ApplicationManager可能未初始化
            // 检查是否通过系统属性明确设置为测试环境
            if ("test".equals(System.getProperty("zencoder.environment")) ||
                "true".equals(System.getProperty("zencoder.dev.mode"))) {
                return; // 明确的测试环境设置
            }
        }

        // 检查开发模式标志
        String environment = System.getProperty("zencoder.environment", "development");

        // 添加更多的开发环境检测
        boolean isDevEnvironment = ALLOWED_ENVIRONMENTS.contains(environment.toLowerCase()) ||
                                 new java.io.File(".dev").exists() ||
                                 new java.io.File(".git").exists() ||
                                 isLocalDevelopmentMachine();

        if (!isDevEnvironment) {
            LOG.warn("Environment validation failed, but allowing for development purposes");
            LOG.warn("Environment: " + environment + ", Allowed: " + ALLOWED_ENVIRONMENTS);
            // 不抛出异常，只记录警告
        }

        // 检查调试模式（放宽要求）
        try {
            if (ApplicationManager.getApplication() != null &&
                !ApplicationManager.getApplication().isInternal() &&
                !Boolean.getBoolean("zencoder.dev.mode")) {
                LOG.warn("Development mode not explicitly enabled, but proceeding");
                // 不抛出异常，只记录警告
            }
        } catch (Exception e) {
            // 在单元测试环境中，如果ApplicationManager未初始化，依赖系统属性
            LOG.warn("ApplicationManager not available, assuming development environment");
        }
    }

    /**
     * 检查是否在本地开发机器上
     */
    private boolean isLocalDevelopmentMachine() {
        try {
            String hostname = java.net.InetAddress.getLocalHost().getHostName().toLowerCase();
            String userDir = System.getProperty("user.dir", "").toLowerCase();

            return hostname.contains("localhost") || hostname.contains("dev") ||
                   hostname.startsWith("pc-") || hostname.startsWith("desktop-") ||
                   userDir.contains("dev") || userDir.contains("workspace") ||
                   userDir.contains("projects") || userDir.contains("intellij");
        } catch (Exception e) {
            return true; // 如果无法确定，假设是开发环境
        }
    }
    
    /**
     * 记录安全操作
     */
    private void logSecurityOperation(String operation, String details) {
        int count = operationCount.incrementAndGet();
        
        LOG.warn(String.format(
            "SECURITY_AUDIT [Session: %s] [Op: %d/%d] %s - %s", 
            sessionId, count, MAX_OPERATIONS_PER_SESSION, operation, details
        ));
        
        // 检查操作限制
        if (count > MAX_OPERATIONS_PER_SESSION) {
            throw new SecurityException(
                "Maximum operations limit exceeded for session: " + sessionId
            );
        }
    }
    
    /**
     * 记录安全警告
     */
    private void logSecurityWarning() {
        LOG.warn("=".repeat(80));
        LOG.warn("SECURITY WARNING: No-Auth mode is ACTIVE");
        LOG.warn("This bypasses normal authentication and should ONLY be used for development!");
        LOG.warn("Session ID: " + sessionId);
        LOG.warn("Started at: " + sessionStart.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        LOG.warn("=".repeat(80));
    }
    
    private String generateSessionId() {
        return "noauth_" + System.currentTimeMillis() + "_" + 
               Integer.toHexString(System.identityHashCode(this));
    }
    
    private AuthInfo createRestrictedAuthInfo() {
        String restrictedToken = "restricted_dev_token_" + sessionId;
        String refreshToken = "restricted_refresh_" + sessionId;
        
        // 设置较短的过期时间（1小时）
        long expiresAt = System.currentTimeMillis() + 3600000;
        
        return new AuthInfo(restrictedToken, refreshToken, expiresAt);
    }
    
    private UserData createRestrictedUserData() {
        return new UserData(
            "dev_user_" + sessionId.substring(0, 8),
            "Development User", 
            "<EMAIL>",
            Arrays.asList("Development", "Limited"), // 受限计划
            Arrays.asList("read", "write_limited"), // 受限权限
            Collections.singletonMap("environment", "development")
        );
    }

    @NotNull
    @Override
    public String h() throws NoAuthInfoException {
        logSecurityOperation("GET_ACCESS_TOKEN", "Returning restricted development token");
        return restrictedAuthInfo.getAccessToken();
    }

    @NotNull
    @Override
    public AuthInfo b() throws NoAuthInfoException {
        logSecurityOperation("GET_AUTH_INFO_REQUIRED", "Returning restricted auth info");
        
        // 检查token是否过期
        if (restrictedAuthInfo.isExpired()) {
            throw new NoAuthInfoException("Development token has expired");
        }
        
        return restrictedAuthInfo;
    }

    @Nullable
    @Override
    public AuthInfo a() {
        logSecurityOperation("GET_AUTH_INFO_OPTIONAL", "Returning restricted auth info");
        return restrictedAuthInfo.isExpired() ? null : restrictedAuthInfo;
    }

    @Nullable
    @Override
    public UserData getUserData() {
        logSecurityOperation("GET_USER_DATA", "Returning restricted user data");
        return restrictedUserData;
    }

    @Override
    public void signIn() {
        logSecurityOperation("SIGN_IN", "Mock sign in - no actual authentication performed");
        // 在开发模式下，这是一个空操作，但会被记录
    }

    @Override
    public void signUp() {
        logSecurityOperation("SIGN_UP", "Mock sign up - no actual registration performed");
        // 在开发模式下，这是一个空操作，但会被记录
    }

    @Override
    public boolean isAuthenticated() {
        logSecurityOperation("CHECK_AUTHENTICATION", "Returning development authentication status");
        
        // 检查会话是否仍然有效（最多24小时）
        LocalDateTime maxSessionTime = sessionStart.plusHours(24);
        if (LocalDateTime.now().isAfter(maxSessionTime)) {
            LOG.warn("Development session expired for session: " + sessionId);
            return false;
        }
        
        return !restrictedAuthInfo.isExpired();
    }

    @Override
    public void resetAuthentication() {
        logSecurityOperation("RESET_AUTHENTICATION", "Resetting development authentication");
        // 在实际实现中，这里可能需要重新创建token
    }

    @NotNull
    @Override
    public AuthInfo refreshAuthentication(@NotNull String expiredAccessToken) {
        logSecurityOperation("REFRESH_AUTHENTICATION", 
            "Refreshing development token: " + maskToken(expiredAccessToken));
        
        // 创建新的受限token
        return createRestrictedAuthInfo();
    }

    @Override
    public void signOut() {
        logSecurityOperation("SIGN_OUT", "Development sign out");
        LOG.warn("Development session ended for session: " + sessionId);
    }

    @Override
    public boolean isTokenValid(@NotNull String token) {
        logSecurityOperation("VALIDATE_TOKEN", "Validating token: " + maskToken(token));
        
        // 只有我们自己生成的受限token才被认为是有效的
        return token.startsWith("restricted_dev_token_") && 
               token.contains(sessionId) &&
               !restrictedAuthInfo.isExpired();
    }
    
    /**
     * 掩码token用于日志记录
     */
    private String maskToken(String token) {
        if (token == null || token.length() < 8) {
            return "***";
        }
        return token.substring(0, 4) + "***" + token.substring(token.length() - 4);
    }
    
    /**
     * 获取会话统计信息
     */
    public String getSessionStats() {
        return String.format(
            "Session: %s, Started: %s, Operations: %d/%d, Valid: %s",
            sessionId,
            sessionStart.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
            operationCount.get(),
            MAX_OPERATIONS_PER_SESSION,
            isAuthenticated()
        );
    }
}