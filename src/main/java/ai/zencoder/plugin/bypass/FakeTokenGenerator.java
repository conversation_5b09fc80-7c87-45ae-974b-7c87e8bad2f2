package ai.zencoder.plugin.bypass;

import com.intellij.openapi.diagnostic.Logger;
import org.jetbrains.annotations.NotNull;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.SecureRandom;
import java.time.Instant;
import java.util.Base64;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * Fake Token Generator
 * Generates realistic-looking JWT tokens and refresh tokens for Zencoder bypass
 */
public class FakeTokenGenerator {
    
    private static final Logger LOG = Logger.getInstance(FakeTokenGenerator.class);
    
    private static final SecureRandom RANDOM = new SecureRandom();
    
    // JWT Header for fake tokens
    private static final String JWT_HEADER = "{\"alg\":\"HS256\",\"typ\":\"JWT\"}";
    
    // Token expiration times
    private static final long ACCESS_TOKEN_VALIDITY_HOURS = 24;
    private static final long REFRESH_TOKEN_VALIDITY_DAYS = 30;
    
    /**
     * Generate a fake JWT access token that looks realistic
     */
    @NotNull
    public static String generateFakeAccessToken() {
        return generateFakeAccessToken("dev-user-" + UUID.randomUUID().toString().substring(0, 8));
    }
    
    /**
     * Generate a fake JWT access token for specific user
     */
    @NotNull
    public static String generateFakeAccessToken(@NotNull String userId) {
        try {
            long now = Instant.now().getEpochSecond();
            long exp = now + TimeUnit.HOURS.toSeconds(ACCESS_TOKEN_VALIDITY_HOURS);
            
            // Create JWT payload with realistic claims
            String payload = String.format(
                "{" +
                "\"sub\":\"%s\"," +
                "\"iss\":\"zencoder-dev\"," +
                "\"aud\":\"zencoder-plugin\"," +
                "\"iat\":%d," +
                "\"exp\":%d," +
                "\"jti\":\"%s\"," +
                "\"scope\":\"read write admin\"," +
                "\"plan\":\"enterprise\"," +
                "\"permissions\":[\"chat\",\"code_generation\",\"multi_repo\",\"advanced_features\"]," +
                "\"subscription_status\":\"active\"," +
                "\"user_type\":\"premium\"" +
                "}",
                userId, now, exp, UUID.randomUUID().toString()
            );
            
            // Encode header and payload
            String encodedHeader = base64UrlEncode(JWT_HEADER.getBytes(StandardCharsets.UTF_8));
            String encodedPayload = base64UrlEncode(payload.getBytes(StandardCharsets.UTF_8));
            
            // Generate fake signature
            String signature = generateFakeSignature(encodedHeader + "." + encodedPayload);
            
            String token = encodedHeader + "." + encodedPayload + "." + signature;
            LOG.debug("Generated fake access token for user: " + userId);
            
            return token;
            
        } catch (Exception e) {
            LOG.error("Failed to generate fake access token", e);
            // Fallback to simple token
            return "fake-access-token-" + UUID.randomUUID().toString();
        }
    }
    
    /**
     * Generate a fake refresh token
     */
    @NotNull
    public static String generateFakeRefreshToken() {
        try {
            // Generate a long, random-looking refresh token
            byte[] randomBytes = new byte[32];
            RANDOM.nextBytes(randomBytes);
            
            String baseToken = base64UrlEncode(randomBytes);
            String timestamp = String.valueOf(Instant.now().getEpochSecond());
            String checksum = generateSimpleChecksum(baseToken + timestamp);
            
            String refreshToken = baseToken + "." + timestamp + "." + checksum;
            LOG.debug("Generated fake refresh token");
            
            return refreshToken;
            
        } catch (Exception e) {
            LOG.error("Failed to generate fake refresh token", e);
            // Fallback to simple token
            return "fake-refresh-token-" + UUID.randomUUID().toString();
        }
    }
    
    /**
     * Generate a fake API key
     */
    @NotNull
    public static String generateFakeApiKey() {
        try {
            String prefix = "zc_dev_";
            byte[] randomBytes = new byte[24];
            RANDOM.nextBytes(randomBytes);
            String randomPart = base64UrlEncode(randomBytes);
            
            return prefix + randomPart;
            
        } catch (Exception e) {
            LOG.error("Failed to generate fake API key", e);
            return "zc_dev_" + UUID.randomUUID().toString().replace("-", "");
        }
    }
    
    /**
     * Generate a fake session ID
     */
    @NotNull
    public static String generateFakeSessionId() {
        return "sess_" + UUID.randomUUID().toString().replace("-", "");
    }
    
    /**
     * Generate a fake user ID
     */
    @NotNull
    public static String generateFakeUserId() {
        return "user_" + UUID.randomUUID().toString().substring(0, 16).replace("-", "");
    }
    
    /**
     * Check if a token looks like a JWT
     */
    public static boolean isJwtFormat(@NotNull String token) {
        return token.split("\\.").length == 3;
    }
    
    /**
     * Extract user ID from JWT token (if possible)
     */
    @NotNull
    public static String extractUserIdFromJwt(@NotNull String jwt) {
        try {
            String[] parts = jwt.split("\\.");
            if (parts.length >= 2) {
                String payload = new String(base64UrlDecode(parts[1]), StandardCharsets.UTF_8);
                // Simple extraction - in real implementation would use JSON parser
                if (payload.contains("\"sub\":\"")) {
                    int start = payload.indexOf("\"sub\":\"") + 7;
                    int end = payload.indexOf("\"", start);
                    if (end > start) {
                        return payload.substring(start, end);
                    }
                }
            }
        } catch (Exception e) {
            LOG.debug("Failed to extract user ID from JWT", e);
        }
        
        return generateFakeUserId();
    }
    
    /**
     * Generate a fake signature for JWT
     */
    @NotNull
    private static String generateFakeSignature(@NotNull String data) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(data.getBytes(StandardCharsets.UTF_8));
            
            // Take first 20 bytes and encode
            byte[] signature = new byte[20];
            System.arraycopy(hash, 0, signature, 0, 20);
            
            return base64UrlEncode(signature);
            
        } catch (Exception e) {
            LOG.debug("Failed to generate fake signature", e);
            // Fallback to random signature
            byte[] randomSignature = new byte[20];
            RANDOM.nextBytes(randomSignature);
            return base64UrlEncode(randomSignature);
        }
    }
    
    /**
     * Generate a simple checksum for tokens
     */
    @NotNull
    private static String generateSimpleChecksum(@NotNull String data) {
        try {
            MessageDigest digest = MessageDigest.getInstance("MD5");
            byte[] hash = digest.digest(data.getBytes(StandardCharsets.UTF_8));
            
            // Take first 8 bytes and encode
            byte[] checksum = new byte[8];
            System.arraycopy(hash, 0, checksum, 0, 8);
            
            return base64UrlEncode(checksum);
            
        } catch (Exception e) {
            LOG.debug("Failed to generate checksum", e);
            return Integer.toHexString(data.hashCode());
        }
    }
    
    /**
     * Base64 URL-safe encoding
     */
    @NotNull
    private static String base64UrlEncode(@NotNull byte[] data) {
        return Base64.getUrlEncoder().withoutPadding().encodeToString(data);
    }
    
    /**
     * Base64 URL-safe decoding
     */
    @NotNull
    private static byte[] base64UrlDecode(@NotNull String data) {
        return Base64.getUrlDecoder().decode(data);
    }
    
    /**
     * Generate a complete fake token set
     */
    @NotNull
    public static FakeTokenSet generateFakeTokenSet() {
        return generateFakeTokenSet(generateFakeUserId());
    }
    
    /**
     * Generate a complete fake token set for specific user
     */
    @NotNull
    public static FakeTokenSet generateFakeTokenSet(@NotNull String userId) {
        return new FakeTokenSet(
            generateFakeAccessToken(userId),
            generateFakeRefreshToken(),
            generateFakeApiKey(),
            generateFakeSessionId(),
            userId
        );
    }
    
    /**
     * Container for a complete set of fake tokens
     */
    public static class FakeTokenSet {
        public final String accessToken;
        public final String refreshToken;
        public final String apiKey;
        public final String sessionId;
        public final String userId;
        
        public FakeTokenSet(@NotNull String accessToken, @NotNull String refreshToken, 
                           @NotNull String apiKey, @NotNull String sessionId, @NotNull String userId) {
            this.accessToken = accessToken;
            this.refreshToken = refreshToken;
            this.apiKey = apiKey;
            this.sessionId = sessionId;
            this.userId = userId;
        }
        
        @Override
        public String toString() {
            return String.format("FakeTokenSet{userId='%s', accessToken='%s...', refreshToken='%s...', apiKey='%s...', sessionId='%s'}",
                    userId,
                    accessToken.length() > 20 ? accessToken.substring(0, 20) : accessToken,
                    refreshToken.length() > 20 ? refreshToken.substring(0, 20) : refreshToken,
                    apiKey.length() > 20 ? apiKey.substring(0, 20) : apiKey,
                    sessionId);
        }
    }
}