package ai.zencoder.plugin.bypass;

import com.intellij.ide.plugins.IdeaPluginDescriptor;
import com.intellij.ide.plugins.PluginManagerCore;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.extensions.PluginId;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * Zencoder Target Discovery
 * Dynamically discovers target classes and methods in Zencoder official plugin
 */
public class ZencoderTargetDiscovery {
    
    private static final Logger LOG = Logger.getInstance(ZencoderTargetDiscovery.class);
    
    // Possible Zencoder plugin IDs
    private static final String[] ZENCODER_PLUGIN_IDS = {
        "ai.zencoder.plugin",
        "com.zencoder.plugin",
        "zencoder-plugin",
        "zencoder",
        "ai.zencoder"
    };
    
    // Target class patterns
    private static final String[] AUTH_SERVICE_PATTERNS = {
        "ai.zencoder.plugin.auth.AuthService",
        "ai.zencoder.plugin.auth.AuthServiceImpl",
        "ai.zencoder.plugin.auth.DefaultAuthService",
        "ai.zencoder.plugin.service.AuthenticationService",
        "com.zencoder.auth.AuthService",
        "com.zencoder.auth.AuthServiceImpl",
        "com.zencoder.plugin.auth.AuthService"
    };
    
    private static final String[] USER_SERVICE_PATTERNS = {
        "ai.zencoder.plugin.user.UserService",
        "ai.zencoder.plugin.user.UserServiceImpl",
        "ai.zencoder.plugin.service.UserService",
        "com.zencoder.user.UserService",
        "com.zencoder.plugin.user.UserService"
    };
    
    private static final String[] AUTH_INFO_PATTERNS = {
        "ai.zencoder.plugin.auth.AuthInfo",
        "ai.zencoder.plugin.model.AuthInfo",
        "com.zencoder.auth.AuthInfo",
        "com.zencoder.model.AuthInfo"
    };
    
    private static final String[] USER_DATA_PATTERNS = {
        "ai.zencoder.plugin.auth.UserData",
        "ai.zencoder.plugin.user.UserData",
        "ai.zencoder.plugin.model.UserData",
        "com.zencoder.user.UserData",
        "com.zencoder.model.UserData"
    };
    
    // Discovery cache
    private static final ConcurrentMap<String, Class<?>> DISCOVERY_CACHE = new ConcurrentHashMap<>();
    private static final ConcurrentMap<String, List<Field>> FIELD_DISCOVERY_CACHE = new ConcurrentHashMap<>();
    private static final ConcurrentMap<String, List<Method>> METHOD_DISCOVERY_CACHE = new ConcurrentHashMap<>();
    
    private static volatile boolean zencoderPluginDetected = false;
    private static volatile String detectedZencoderPluginId = null;
    
    /**
     * Detect if Zencoder official plugin is installed and enabled
     */
    public static boolean detectZencoderPlugin() {
        if (zencoderPluginDetected) {
            return true;
        }
        
        try {
            for (String pluginId : ZENCODER_PLUGIN_IDS) {
                IdeaPluginDescriptor plugin = PluginManagerCore.getPlugin(PluginId.getId(pluginId));
                if (plugin != null && plugin.isEnabled()) {
                    zencoderPluginDetected = true;
                    detectedZencoderPluginId = pluginId;
                    LOG.info("Detected Zencoder plugin: " + pluginId + " v" + plugin.getVersion());
                    return true;
                }
            }
            
            // Alternative detection: try to find known Zencoder classes
            for (String pattern : AUTH_SERVICE_PATTERNS) {
                try {
                    Class.forName(pattern);
                    zencoderPluginDetected = true;
                    detectedZencoderPluginId = "detected-by-class";
                    LOG.info("Detected Zencoder plugin by class presence: " + pattern);
                    return true;
                } catch (ClassNotFoundException ignored) {
                    // Continue searching
                }
            }
            
            LOG.warn("Zencoder official plugin not detected");
            return false;
            
        } catch (Exception e) {
            LOG.error("Failed to detect Zencoder plugin", e);
            return false;
        }
    }
    
    /**
     * Get detected Zencoder plugin ID
     */
    @Nullable
    public static String getDetectedZencoderPluginId() {
        return detectedZencoderPluginId;
    }
    
    /**
     * Discover AuthService class
     */
    @Nullable
    public static Class<?> discoverAuthServiceClass() {
        String cacheKey = "AuthService";
        Class<?> cached = DISCOVERY_CACHE.get(cacheKey);
        if (cached != null) {
            return cached;
        }
        
        for (String pattern : AUTH_SERVICE_PATTERNS) {
            try {
                Class<?> clazz = Class.forName(pattern);
                DISCOVERY_CACHE.put(cacheKey, clazz);
                LOG.info("Discovered AuthService class: " + pattern);
                return clazz;
            } catch (ClassNotFoundException ignored) {
                // Continue searching
            }
        }
        
        LOG.warn("AuthService class not found");
        return null;
    }
    
    /**
     * Discover UserService class
     */
    @Nullable
    public static Class<?> discoverUserServiceClass() {
        String cacheKey = "UserService";
        Class<?> cached = DISCOVERY_CACHE.get(cacheKey);
        if (cached != null) {
            return cached;
        }
        
        for (String pattern : USER_SERVICE_PATTERNS) {
            try {
                Class<?> clazz = Class.forName(pattern);
                DISCOVERY_CACHE.put(cacheKey, clazz);
                LOG.info("Discovered UserService class: " + pattern);
                return clazz;
            } catch (ClassNotFoundException ignored) {
                // Continue searching
            }
        }
        
        LOG.warn("UserService class not found");
        return null;
    }
    
    /**
     * Discover AuthInfo class
     */
    @Nullable
    public static Class<?> discoverAuthInfoClass() {
        String cacheKey = "AuthInfo";
        Class<?> cached = DISCOVERY_CACHE.get(cacheKey);
        if (cached != null) {
            return cached;
        }
        
        for (String pattern : AUTH_INFO_PATTERNS) {
            try {
                Class<?> clazz = Class.forName(pattern);
                DISCOVERY_CACHE.put(cacheKey, clazz);
                LOG.info("Discovered AuthInfo class: " + pattern);
                return clazz;
            } catch (ClassNotFoundException ignored) {
                // Continue searching
            }
        }
        
        LOG.warn("AuthInfo class not found");
        return null;
    }
    
    /**
     * Discover UserData class
     */
    @Nullable
    public static Class<?> discoverUserDataClass() {
        String cacheKey = "UserData";
        Class<?> cached = DISCOVERY_CACHE.get(cacheKey);
        if (cached != null) {
            return cached;
        }
        
        for (String pattern : USER_DATA_PATTERNS) {
            try {
                Class<?> clazz = Class.forName(pattern);
                DISCOVERY_CACHE.put(cacheKey, clazz);
                LOG.info("Discovered UserData class: " + pattern);
                return clazz;
            } catch (ClassNotFoundException ignored) {
                // Continue searching
            }
        }
        
        LOG.warn("UserData class not found");
        return null;
    }
    
    /**
     * Discover authentication-related fields in a class
     */
    @NotNull
    public static List<Field> discoverAuthFields(@NotNull Class<?> targetClass) {
        String cacheKey = targetClass.getName() + ":authFields";
        List<Field> cached = FIELD_DISCOVERY_CACHE.get(cacheKey);
        if (cached != null) {
            return cached;
        }
        
        List<Field> authFields = new ArrayList<>();
        
        try {
            Field[] allFields = targetClass.getDeclaredFields();
            
            for (Field field : allFields) {
                String fieldName = field.getName().toLowerCase();
                String fieldType = field.getType().getSimpleName().toLowerCase();
                
                // Check for token-related fields
                if (fieldName.contains("token") || fieldName.contains("auth") || 
                    fieldName.contains("credential") || fieldName.contains("jwt") ||
                    fieldType.contains("string") && (fieldName.contains("access") || fieldName.contains("bearer"))) {
                    authFields.add(field);
                    LOG.debug("Found auth field: " + field.getName() + " (" + field.getType().getSimpleName() + ")");
                }
                
                // Check for user data fields
                if (fieldName.contains("user") || fieldName.contains("profile") || 
                    fieldType.contains("userdata") || fieldType.contains("user")) {
                    authFields.add(field);
                    LOG.debug("Found user field: " + field.getName() + " (" + field.getType().getSimpleName() + ")");
                }
                
                // Check for state fields
                if (fieldName.contains("state") || fieldName.contains("authenticated") || 
                    fieldName.contains("logged") || fieldType.contains("boolean") && fieldName.contains("auth")) {
                    authFields.add(field);
                    LOG.debug("Found state field: " + field.getName() + " (" + field.getType().getSimpleName() + ")");
                }
            }
            
            FIELD_DISCOVERY_CACHE.put(cacheKey, authFields);
            LOG.info("Discovered " + authFields.size() + " auth fields in " + targetClass.getSimpleName());
            
        } catch (Exception e) {
            LOG.error("Failed to discover auth fields in " + targetClass.getName(), e);
        }
        
        return authFields;
    }
    
    /**
     * Discover authentication-related methods in a class
     */
    @NotNull
    public static List<Method> discoverAuthMethods(@NotNull Class<?> targetClass) {
        String cacheKey = targetClass.getName() + ":authMethods";
        List<Method> cached = METHOD_DISCOVERY_CACHE.get(cacheKey);
        if (cached != null) {
            return cached;
        }
        
        List<Method> authMethods = new ArrayList<>();
        
        try {
            Method[] allMethods = targetClass.getDeclaredMethods();
            
            for (Method method : allMethods) {
                String methodName = method.getName().toLowerCase();
                
                // Check for authentication methods
                if (methodName.contains("auth") || methodName.contains("login") || 
                    methodName.contains("signin") || methodName.contains("token") ||
                    methodName.contains("credential") || methodName.contains("validate")) {
                    authMethods.add(method);
                    LOG.debug("Found auth method: " + method.getName());
                }
                
                // Check for user-related methods
                if (methodName.contains("user") || methodName.contains("profile") || 
                    methodName.contains("whoami") || methodName.contains("getuser")) {
                    authMethods.add(method);
                    LOG.debug("Found user method: " + method.getName());
                }
                
                // Check for state methods
                if (methodName.startsWith("is") && (methodName.contains("auth") || methodName.contains("logged") || 
                    methodName.contains("valid")) || methodName.contains("check")) {
                    authMethods.add(method);
                    LOG.debug("Found state method: " + method.getName());
                }
            }
            
            METHOD_DISCOVERY_CACHE.put(cacheKey, authMethods);
            LOG.info("Discovered " + authMethods.size() + " auth methods in " + targetClass.getSimpleName());
            
        } catch (Exception e) {
            LOG.error("Failed to discover auth methods in " + targetClass.getName(), e);
        }
        
        return authMethods;
    }
    
    /**
     * Perform comprehensive discovery of all Zencoder targets
     */
    @NotNull
    public static ZencoderTargetInfo performFullDiscovery() {
        LOG.info("Starting comprehensive Zencoder target discovery...");
        
        ZencoderTargetInfo info = new ZencoderTargetInfo();
        
        // Detect plugin presence
        info.pluginDetected = detectZencoderPlugin();
        info.pluginId = detectedZencoderPluginId;
        
        if (!info.pluginDetected) {
            LOG.warn("Zencoder plugin not detected, discovery may be incomplete");
        }
        
        // Discover classes
        info.authServiceClass = discoverAuthServiceClass();
        info.userServiceClass = discoverUserServiceClass();
        info.authInfoClass = discoverAuthInfoClass();
        info.userDataClass = discoverUserDataClass();
        
        // Discover fields and methods
        if (info.authServiceClass != null) {
            info.authServiceFields = discoverAuthFields(info.authServiceClass);
            info.authServiceMethods = discoverAuthMethods(info.authServiceClass);
        }
        
        if (info.userServiceClass != null) {
            info.userServiceFields = discoverAuthFields(info.userServiceClass);
            info.userServiceMethods = discoverAuthMethods(info.userServiceClass);
        }
        
        LOG.info("Discovery completed: " + info);
        return info;
    }
    
    /**
     * Clear discovery cache
     */
    public static void clearDiscoveryCache() {
        DISCOVERY_CACHE.clear();
        FIELD_DISCOVERY_CACHE.clear();
        METHOD_DISCOVERY_CACHE.clear();
        zencoderPluginDetected = false;
        detectedZencoderPluginId = null;
        LOG.info("Discovery cache cleared");
    }
    
    /**
     * Container for discovery results
     */
    public static class ZencoderTargetInfo {
        public boolean pluginDetected = false;
        public String pluginId = null;
        
        public Class<?> authServiceClass = null;
        public Class<?> userServiceClass = null;
        public Class<?> authInfoClass = null;
        public Class<?> userDataClass = null;
        
        public List<Field> authServiceFields = new ArrayList<>();
        public List<Method> authServiceMethods = new ArrayList<>();
        public List<Field> userServiceFields = new ArrayList<>();
        public List<Method> userServiceMethods = new ArrayList<>();
        
        @Override
        public String toString() {
            return String.format("ZencoderTargetInfo{plugin=%s, authService=%s, userService=%s, authInfo=%s, userData=%s, fields=%d, methods=%d}",
                    pluginDetected ? pluginId : "not detected",
                    authServiceClass != null ? authServiceClass.getSimpleName() : "null",
                    userServiceClass != null ? userServiceClass.getSimpleName() : "null",
                    authInfoClass != null ? authInfoClass.getSimpleName() : "null",
                    userDataClass != null ? userDataClass.getSimpleName() : "null",
                    authServiceFields.size() + userServiceFields.size(),
                    authServiceMethods.size() + userServiceMethods.size());
        }
    }
}