package ai.zencoder.plugin.bypass;

import ai.zencoder.plugin.noauth.EnvironmentSecurityChecker;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.startup.ProjectActivity;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * Zencoder Bypass Initializer
 * Initializes the complete authentication bypass system on project startup
 */
public class ZencoderBypassInitializer implements ProjectActivity {
    
    private static final Logger LOG = Logger.getInstance(ZencoderBypassInitializer.class);
    
    // Initialization delays
    private static final int INITIAL_DELAY_MS = 3000; // 3 seconds
    private static final int ZENCODER_DETECTION_TIMEOUT_MS = 10000; // 10 seconds
    private static final int MAX_INITIALIZATION_ATTEMPTS = 3;
    
    private static volatile boolean initializationCompleted = false;
    private static volatile boolean initializationInProgress = false;
    
    @Nullable
    @Override
    public Object execute(@NotNull Project project, @NotNull Continuation<? super Unit> continuation) {
        if (initializationCompleted || initializationInProgress) {
            LOG.debug("Bypass initialization already completed or in progress");
            return Unit.INSTANCE;
        }
        
        LOG.info("Starting Zencoder bypass system initialization for project: " + project.getName());
        
        // Delay initialization to ensure Zencoder official plugin is loaded
        ApplicationManager.getApplication().invokeLater(() -> {
            CompletableFuture.runAsync(() -> initializeBypassSystem(project))
                .orTimeout(30, TimeUnit.SECONDS)
                .whenComplete((result, throwable) -> {
                    if (throwable != null) {
                        LOG.error("Bypass system initialization failed", throwable);
                        initializationInProgress = false;
                    } else {
                        LOG.info("Bypass system initialization completed");
                        initializationCompleted = true;
                        initializationInProgress = false;
                    }
                });
        }, ApplicationManager.getApplication().getDefaultModalityState());
        
        return Unit.INSTANCE;
    }
    
    /**
     * Initialize the complete bypass system
     */
    private void initializeBypassSystem(@NotNull Project project) {
        try {
            initializationInProgress = true;
            LOG.info("Initializing Zencoder authentication bypass system...");
            
            // Step 1: Security and environment checks
            if (!performSecurityChecks()) {
                LOG.error("Security checks failed, aborting bypass initialization");
                return;
            }
            
            // Step 2: Wait for Zencoder plugin to load
            if (!waitForZencoderPlugin()) {
                LOG.warn("Zencoder official plugin not detected, proceeding with limited functionality");
            }
            
            // Step 3: Perform target discovery
            ZencoderTargetDiscovery.ZencoderTargetInfo targetInfo = performTargetDiscovery();
            if (!targetInfo.pluginDetected) {
                LOG.warn("Zencoder plugin not fully detected, bypass may have limited effectiveness");
            }
            
            // Step 4: Initialize bypass components with retries
            if (!initializeBypassComponents(targetInfo)) {
                LOG.error("Failed to initialize bypass components");
                return;
            }
            
            // Step 5: Start state synchronization
            startStateSynchronization();
            
            // Step 6: Verify bypass system
            if (verifyBypassSystem()) {
                LOG.info("Zencoder bypass system initialized successfully!");
                logBypassStatus();
            } else {
                LOG.error("Bypass system verification failed");
            }
            
        } catch (Exception e) {
            LOG.error("Failed to initialize bypass system", e);
            initializationInProgress = false;
        }
    }
    
    /**
     * Perform security and environment checks
     */
    private boolean performSecurityChecks() {
        try {
            LOG.debug("Performing security checks...");
            
            EnvironmentSecurityChecker checker = new EnvironmentSecurityChecker();
            EnvironmentSecurityChecker.SecurityCheckResult result = checker.performSecurityCheck();
            
            if (!result.isSafe()) {
                LOG.error("Security check failed: " + result.getErrors());
                return false;
            }
            
            if (!result.getWarnings().isEmpty()) {
                LOG.warn("Security warnings: " + result.getWarnings());
            }
            
            LOG.info("Security checks passed");
            return true;
            
        } catch (Exception e) {
            LOG.error("Security check failed with exception", e);
            return false;
        }
    }
    
    /**
     * Wait for Zencoder official plugin to load
     */
    private boolean waitForZencoderPlugin() {
        try {
            LOG.debug("Waiting for Zencoder plugin to load...");
            
            long startTime = System.currentTimeMillis();
            long timeout = startTime + ZENCODER_DETECTION_TIMEOUT_MS;
            
            while (System.currentTimeMillis() < timeout) {
                if (ZencoderTargetDiscovery.detectZencoderPlugin()) {
                    LOG.info("Zencoder plugin detected: " + ZencoderTargetDiscovery.getDetectedZencoderPluginId());
                    return true;
                }
                
                Thread.sleep(500); // Wait 500ms between checks
            }
            
            LOG.warn("Zencoder plugin detection timed out");
            return false;
            
        } catch (InterruptedException e) {
            LOG.warn("Zencoder plugin detection interrupted", e);
            Thread.currentThread().interrupt();
            return false;
        } catch (Exception e) {
            LOG.error("Failed to detect Zencoder plugin", e);
            return false;
        }
    }
    
    /**
     * Perform comprehensive target discovery
     */
    @NotNull
    private ZencoderTargetDiscovery.ZencoderTargetInfo performTargetDiscovery() {
        try {
            LOG.debug("Performing target discovery...");
            
            ZencoderTargetDiscovery.ZencoderTargetInfo targetInfo = ZencoderTargetDiscovery.performFullDiscovery();
            
            LOG.info("Target discovery completed: " + targetInfo);
            return targetInfo;
            
        } catch (Exception e) {
            LOG.error("Target discovery failed", e);
            return new ZencoderTargetDiscovery.ZencoderTargetInfo(); // Return empty info
        }
    }
    
    /**
     * Initialize bypass components with retry logic
     */
    private boolean initializeBypassComponents(@NotNull ZencoderTargetDiscovery.ZencoderTargetInfo targetInfo) {
        for (int attempt = 1; attempt <= MAX_INITIALIZATION_ATTEMPTS; attempt++) {
            try {
                LOG.info("Initializing bypass components (attempt " + attempt + "/" + MAX_INITIALIZATION_ATTEMPTS + ")...");
                
                // Step 1: Service hijacking
                if (!ZencoderServiceHijacker.hijackAllServices()) {
                    LOG.warn("Service hijacking failed on attempt " + attempt);
                    if (attempt == MAX_INITIALIZATION_ATTEMPTS) {
                        return false;
                    }
                    Thread.sleep(2000); // Wait before retry
                    continue;
                }
                
                // Step 2: Credential injection
                FakeUserDataBuilder.FakeUserProfile profile = FakeUserDataBuilder.buildCompleteProfile();
                if (!ZencoderCredentialInjector.injectFakeCredentials(profile)) {
                    LOG.warn("Credential injection failed on attempt " + attempt);
                    if (attempt == MAX_INITIALIZATION_ATTEMPTS) {
                        return false;
                    }
                    Thread.sleep(2000); // Wait before retry
                    continue;
                }
                
                LOG.info("Bypass components initialized successfully on attempt " + attempt);
                return true;
                
            } catch (Exception e) {
                LOG.error("Bypass component initialization failed on attempt " + attempt, e);
                if (attempt == MAX_INITIALIZATION_ATTEMPTS) {
                    return false;
                }
                
                try {
                    Thread.sleep(2000); // Wait before retry
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    return false;
                }
            }
        }
        
        return false;
    }
    
    /**
     * Start state synchronization
     */
    private void startStateSynchronization() {
        try {
            LOG.debug("Starting state synchronization...");
            
            // Load any persisted state first
            ZencoderStateSynchronizer.loadAuthState();
            
            // Start monitoring
            ZencoderStateSynchronizer.startStateSynchronization();
            
            LOG.info("State synchronization started");
            
        } catch (Exception e) {
            LOG.error("Failed to start state synchronization", e);
        }
    }
    
    /**
     * Verify that the bypass system is working correctly
     */
    private boolean verifyBypassSystem() {
        try {
            LOG.debug("Verifying bypass system...");
            
            // Check service hijacking
            if (!ZencoderServiceHijacker.isHijackActive()) {
                LOG.error("Service hijacking verification failed");
                return false;
            }
            
            // Check credential injection
            if (!ZencoderCredentialInjector.isInjectionActive()) {
                LOG.error("Credential injection verification failed");
                return false;
            }
            
            // Check state synchronization
            if (!ZencoderStateSynchronizer.isMonitoringActive()) {
                LOG.error("State synchronization verification failed");
                return false;
            }
            
            // Check if we can retrieve injected credentials
            if (ZencoderCredentialInjector.getCurrentAuthInfo() == null) {
                LOG.error("Cannot retrieve injected AuthInfo");
                return false;
            }
            
            if (ZencoderCredentialInjector.getCurrentUserData() == null) {
                LOG.error("Cannot retrieve injected UserData");
                return false;
            }
            
            // Check reflection access
            String accessToken = ZencoderReflectionUtils.getAccessToken();
            if (accessToken == null) {
                LOG.warn("Cannot retrieve access token via reflection (may be normal)");
            }
            
            LOG.info("Bypass system verification passed");
            return true;
            
        } catch (Exception e) {
            LOG.error("Bypass system verification failed", e);
            return false;
        }
    }
    
    /**
     * Log current bypass status
     */
    private void logBypassStatus() {
        try {
            LOG.info("=== Zencoder Bypass System Status ===");
            LOG.info("Service Hijacking: " + ZencoderServiceHijacker.getHijackStatus());
            LOG.info("Credential Injection: " + (ZencoderCredentialInjector.isInjectionActive() ? "ACTIVE" : "INACTIVE"));
            LOG.info("State Synchronization: " + ZencoderStateSynchronizer.getSyncStatus());
            LOG.info("Reflection Cache: " + ZencoderReflectionUtils.getCacheStats());
            
            // Log user info
            var userData = ZencoderCredentialInjector.getCurrentUserData();
            if (userData != null) {
                LOG.info("Bypass User: " + userData.getDisplayName() + " (" + userData.getEmail() + ")");
                LOG.info("User Plans: " + userData.getPlans());
                LOG.info("User Permissions: " + userData.getPermissions().size() + " permissions");
                LOG.info("VIP Status: " + (userData.isVip() ? "ACTIVE" : "INACTIVE"));
            }
            
            LOG.info("=====================================");
            
        } catch (Exception e) {
            LOG.debug("Failed to log bypass status", e);
        }
    }
    
    /**
     * Check if initialization is completed
     */
    public static boolean isInitializationCompleted() {
        return initializationCompleted;
    }
    
    /**
     * Check if initialization is in progress
     */
    public static boolean isInitializationInProgress() {
        return initializationInProgress;
    }
    
    /**
     * Force re-initialization (for testing or recovery)
     */
    public static void forceReinitialization(@NotNull Project project) {
        LOG.info("Forcing bypass system re-initialization...");
        
        initializationCompleted = false;
        initializationInProgress = false;
        
        // Clear caches
        ZencoderReflectionUtils.clearCaches();
        ZencoderTargetDiscovery.clearDiscoveryCache();
        
        // Stop current synchronization
        ZencoderStateSynchronizer.stopStateSynchronization();
        
        // Start fresh initialization
        new ZencoderBypassInitializer().initializeBypassSystem(project);
    }
    
    /**
     * Shutdown bypass system
     */
    public static void shutdownBypassSystem() {
        try {
            LOG.info("Shutting down bypass system...");
            
            // Stop synchronization
            ZencoderStateSynchronizer.stopStateSynchronization();
            
            // Clear credentials
            ZencoderCredentialInjector.clearInjectedCredentials();
            
            // Restore services
            ZencoderServiceHijacker.restoreOriginalServices();
            
            // Clear caches
            ZencoderReflectionUtils.clearCaches();
            ZencoderTargetDiscovery.clearDiscoveryCache();
            
            // Reset flags
            initializationCompleted = false;
            initializationInProgress = false;
            
            LOG.info("Bypass system shutdown completed");
            
        } catch (Exception e) {
            LOG.error("Failed to shutdown bypass system", e);
        }
    }
}