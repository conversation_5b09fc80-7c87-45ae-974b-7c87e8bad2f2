package ai.zencoder.plugin.bypass;

import ai.zencoder.plugin.api.NoAuthService;
import ai.zencoder.plugin.api.SecureNoAuthService;
import ai.zencoder.plugin.auth.AuthService;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Zencoder Service Hijacker
 * Dynamically replaces Zencoder official plugin services with bypass implementations
 */
public class ZencoderServiceHijacker {
    
    private static final Logger LOG = Logger.getInstance(ZencoderServiceHijacker.class);
    
    // Service replacement mappings
    private static final Map<String, String> SERVICE_REPLACEMENTS = Map.of(
        "ai.zencoder.plugin.auth.AuthService", "ai.zencoder.plugin.api.SecureNoAuthService",
        "ai.zencoder.plugin.auth.AuthServiceImpl", "ai.zencoder.plugin.api.SecureNoAuthService",
        "ai.zencoder.plugin.user.UserService", "ai.zencoder.plugin.api.NoAuthService",
        "ai.zencoder.plugin.billing.BillingService", "ai.zencoder.plugin.api.NoAuthService"
    );
    
    // Hijack status tracking
    private static final AtomicBoolean HIJACK_ACTIVE = new AtomicBoolean(false);
    private static final Map<String, Object> ORIGINAL_SERVICES = new ConcurrentHashMap<>();
    private static final Map<String, Object> HIJACKED_SERVICES = new ConcurrentHashMap<>();
    
    /**
     * Perform complete service hijacking
     */
    public static boolean hijackAllServices() {
        try {
            LOG.info("Starting comprehensive service hijacking...");
            
            boolean success = true;
            
            // Hijack AuthService
            if (!hijackAuthService()) {
                LOG.warn("Failed to hijack AuthService");
                success = false;
            }
            
            // Hijack UserService
            if (!hijackUserService()) {
                LOG.warn("Failed to hijack UserService");
                success = false;
            }
            
            // Hijack BillingService
            if (!hijackBillingService()) {
                LOG.warn("Failed to hijack BillingService");
                success = false;
            }
            
            if (success) {
                HIJACK_ACTIVE.set(true);
                LOG.info("Service hijacking completed successfully");
            } else {
                LOG.error("Service hijacking partially failed");
            }
            
            return success;
            
        } catch (Exception e) {
            LOG.error("Failed to hijack services", e);
            return false;
        }
    }
    
    /**
     * Hijack AuthService with SecureNoAuthService
     */
    public static boolean hijackAuthService() {
        try {
            LOG.debug("Attempting to hijack AuthService...");
            
            // Find the original AuthService
            Object originalAuthService = ZencoderReflectionUtils.getAuthServiceInstance();
            if (originalAuthService == null) {
                LOG.warn("Original AuthService not found, cannot hijack");
                return false;
            }
            
            String originalClassName = originalAuthService.getClass().getName();
            LOG.info("Found original AuthService: " + originalClassName);
            
            // Store original service
            ORIGINAL_SERVICES.put("AuthService", originalAuthService);
            
            // Create replacement service
            AuthService replacementService = new SecureNoAuthService();
            HIJACKED_SERVICES.put("AuthService", replacementService);
            
            // Try to replace the service in the application service manager
            if (replaceApplicationService(AuthService.class, replacementService)) {
                LOG.info("Successfully hijacked AuthService with SecureNoAuthService");
                return true;
            }
            
            // Try alternative hijacking methods
            if (hijackServiceViaReflection(originalAuthService, replacementService)) {
                LOG.info("Successfully hijacked AuthService via reflection");
                return true;
            }
            
            LOG.warn("Failed to hijack AuthService");
            return false;
            
        } catch (Exception e) {
            LOG.error("Failed to hijack AuthService", e);
            return false;
        }
    }
    
    /**
     * Hijack UserService with NoAuthService
     */
    public static boolean hijackUserService() {
        try {
            LOG.debug("Attempting to hijack UserService...");
            
            // Find the original UserService
            Object originalUserService = ZencoderReflectionUtils.getUserServiceInstance();
            if (originalUserService == null) {
                LOG.debug("Original UserService not found, skipping hijack");
                return true; // Not critical if UserService doesn't exist
            }
            
            String originalClassName = originalUserService.getClass().getName();
            LOG.info("Found original UserService: " + originalClassName);
            
            // Store original service
            ORIGINAL_SERVICES.put("UserService", originalUserService);
            
            // Create replacement service
            Object replacementService = new NoAuthService();
            HIJACKED_SERVICES.put("UserService", replacementService);
            
            // Try to replace the service
            Class<?> userServiceClass = originalUserService.getClass();
            if (replaceApplicationService(userServiceClass, replacementService)) {
                LOG.info("Successfully hijacked UserService with NoAuthService");
                return true;
            }
            
            // Try alternative hijacking methods
            if (hijackServiceViaReflection(originalUserService, replacementService)) {
                LOG.info("Successfully hijacked UserService via reflection");
                return true;
            }
            
            LOG.warn("Failed to hijack UserService");
            return false;
            
        } catch (Exception e) {
            LOG.error("Failed to hijack UserService", e);
            return false;
        }
    }
    
    /**
     * Hijack BillingService with NoAuthService
     */
    public static boolean hijackBillingService() {
        try {
            LOG.debug("Attempting to hijack BillingService...");
            
            // Try to find BillingService
            Class<?> billingServiceClass = ZencoderReflectionUtils.findClass("ai.zencoder.plugin.billing.BillingService");
            if (billingServiceClass == null) {
                billingServiceClass = ZencoderReflectionUtils.findClass("com.zencoder.billing.BillingService");
            }
            
            if (billingServiceClass == null) {
                LOG.debug("BillingService class not found, skipping hijack");
                return true; // Not critical if BillingService doesn't exist
            }
            
            // Get the service instance
            Object originalBillingService = ApplicationManager.getApplication().getService(billingServiceClass);
            if (originalBillingService == null) {
                LOG.debug("BillingService instance not found, skipping hijack");
                return true;
            }
            
            LOG.info("Found original BillingService: " + billingServiceClass.getName());
            
            // Store original service
            ORIGINAL_SERVICES.put("BillingService", originalBillingService);
            
            // Create replacement service
            Object replacementService = new NoAuthService();
            HIJACKED_SERVICES.put("BillingService", replacementService);
            
            // Try to replace the service
            if (replaceApplicationService(billingServiceClass, replacementService)) {
                LOG.info("Successfully hijacked BillingService with NoAuthService");
                return true;
            }
            
            LOG.warn("Failed to hijack BillingService");
            return false;
            
        } catch (Exception e) {
            LOG.error("Failed to hijack BillingService", e);
            return false;
        }
    }
    
    /**
     * Replace service in application service manager
     */
    private static boolean replaceApplicationService(@NotNull Class<?> serviceClass, @NotNull Object newService) {
        try {
            // Get the application service manager
            Object application = ApplicationManager.getApplication();
            Object serviceManager = getServiceManager(application);
            
            if (serviceManager == null) {
                LOG.debug("Service manager not accessible");
                return false;
            }
            
            // Try to replace the service
            if (replaceServiceInManager(serviceManager, serviceClass, newService)) {
                LOG.debug("Successfully replaced service in manager: " + serviceClass.getSimpleName());
                return true;
            }
            
            return false;
            
        } catch (Exception e) {
            LOG.debug("Failed to replace service in application manager", e);
            return false;
        }
    }
    
    /**
     * Get service manager from application
     */
    @Nullable
    private static Object getServiceManager(@NotNull Object application) {
        try {
            // Try different possible field names for service manager
            String[] serviceManagerFields = {
                "myServiceManager", "serviceManager", "myComponentManager", "componentManager"
            };
            
            Class<?> appClass = application.getClass();
            
            for (String fieldName : serviceManagerFields) {
                try {
                    Field field = appClass.getDeclaredField(fieldName);
                    field.setAccessible(true);
                    Object serviceManager = field.get(application);
                    if (serviceManager != null) {
                        LOG.debug("Found service manager via field: " + fieldName);
                        return serviceManager;
                    }
                } catch (NoSuchFieldException ignored) {
                    // Continue searching
                }
            }
            
            // Try to get service manager via method
            Method getServiceManagerMethod = ZencoderReflectionUtils.findMethod(appClass, "getServiceManager");
            if (getServiceManagerMethod != null) {
                Object serviceManager = ZencoderReflectionUtils.invokeMethod(application, "getServiceManager");
                if (serviceManager != null) {
                    LOG.debug("Found service manager via method");
                    return serviceManager;
                }
            }
            
            return null;
            
        } catch (Exception e) {
            LOG.debug("Failed to get service manager", e);
            return null;
        }
    }
    
    /**
     * Replace service in service manager
     */
    private static boolean replaceServiceInManager(@NotNull Object serviceManager, 
                                                  @NotNull Class<?> serviceClass, 
                                                  @NotNull Object newService) {
        try {
            Class<?> managerClass = serviceManager.getClass();
            
            // Try to find service registry/map
            String[] registryFields = {
                "myServices", "services", "myServiceMap", "serviceMap", "myRegistry", "registry"
            };
            
            for (String fieldName : registryFields) {
                try {
                    Field field = managerClass.getDeclaredField(fieldName);
                    field.setAccessible(true);
                    Object registry = field.get(serviceManager);
                    
                    if (registry instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<Object, Object> serviceMap = (Map<Object, Object>) registry;
                        
                        // Try different key formats
                        Object[] possibleKeys = {
                            serviceClass,
                            serviceClass.getName(),
                            serviceClass.getSimpleName()
                        };
                        
                        for (Object key : possibleKeys) {
                            if (serviceMap.containsKey(key)) {
                                serviceMap.put(key, newService);
                                LOG.debug("Replaced service in registry with key: " + key);
                                return true;
                            }
                        }
                        
                        // If not found, try to add it
                        serviceMap.put(serviceClass, newService);
                        LOG.debug("Added new service to registry: " + serviceClass.getSimpleName());
                        return true;
                    }
                    
                } catch (NoSuchFieldException ignored) {
                    // Continue searching
                }
            }
            
            return false;
            
        } catch (Exception e) {
            LOG.debug("Failed to replace service in manager", e);
            return false;
        }
    }
    
    /**
     * Hijack service via direct reflection on the instance
     */
    private static boolean hijackServiceViaReflection(@NotNull Object originalService, @NotNull Object replacementService) {
        try {
            // This is a more aggressive approach - replace the instance's internal state
            Class<?> originalClass = originalService.getClass();
            Class<?> replacementClass = replacementService.getClass();
            
            // Copy compatible fields from replacement to original
            Field[] replacementFields = replacementClass.getDeclaredFields();
            
            for (Field replacementField : replacementFields) {
                try {
                    Field originalField = originalClass.getDeclaredField(replacementField.getName());
                    if (originalField.getType().isAssignableFrom(replacementField.getType())) {
                        replacementField.setAccessible(true);
                        Object value = replacementField.get(replacementService);
                        
                        if (ZencoderReflectionUtils.modifyField(originalField, originalService, value)) {
                            LOG.debug("Copied field: " + replacementField.getName());
                        }
                    }
                } catch (NoSuchFieldException ignored) {
                    // Field doesn't exist in original, skip
                }
            }
            
            return true;
            
        } catch (Exception e) {
            LOG.debug("Failed to hijack service via reflection", e);
            return false;
        }
    }
    
    /**
     * Restore original services
     */
    public static boolean restoreOriginalServices() {
        try {
            LOG.info("Restoring original services...");
            
            boolean success = true;
            
            for (Map.Entry<String, Object> entry : ORIGINAL_SERVICES.entrySet()) {
                String serviceName = entry.getKey();
                Object originalService = entry.getValue();
                
                try {
                    if (restoreService(serviceName, originalService)) {
                        LOG.debug("Restored service: " + serviceName);
                    } else {
                        LOG.warn("Failed to restore service: " + serviceName);
                        success = false;
                    }
                } catch (Exception e) {
                    LOG.error("Failed to restore service: " + serviceName, e);
                    success = false;
                }
            }
            
            // Clear tracking maps
            ORIGINAL_SERVICES.clear();
            HIJACKED_SERVICES.clear();
            HIJACK_ACTIVE.set(false);
            
            if (success) {
                LOG.info("All services restored successfully");
            } else {
                LOG.warn("Some services failed to restore");
            }
            
            return success;
            
        } catch (Exception e) {
            LOG.error("Failed to restore original services", e);
            return false;
        }
    }
    
    /**
     * Restore a specific service
     */
    private static boolean restoreService(@NotNull String serviceName, @NotNull Object originalService) {
        try {
            Class<?> serviceClass = originalService.getClass();
            
            // Try to restore in application service manager
            return replaceApplicationService(serviceClass, originalService);
            
        } catch (Exception e) {
            LOG.debug("Failed to restore service: " + serviceName, e);
            return false;
        }
    }
    
    /**
     * Check if a specific service is hijacked
     */
    public static boolean isServiceHijacked(@NotNull String serviceClassName) {
        return HIJACKED_SERVICES.containsKey(serviceClassName) || 
               ORIGINAL_SERVICES.containsKey(serviceClassName);
    }
    
    /**
     * Check if any services are currently hijacked
     */
    public static boolean isHijackActive() {
        return HIJACK_ACTIVE.get();
    }
    
    /**
     * Get hijacked service instance
     */
    @Nullable
    public static Object getHijackedService(@NotNull String serviceName) {
        return HIJACKED_SERVICES.get(serviceName);
    }
    
    /**
     * Get original service instance
     */
    @Nullable
    public static Object getOriginalService(@NotNull String serviceName) {
        return ORIGINAL_SERVICES.get(serviceName);
    }
    
    /**
     * Get hijack status summary
     */
    @NotNull
    public static String getHijackStatus() {
        return String.format("ServiceHijack{active=%s, hijacked=%d, original=%d}",
                HIJACK_ACTIVE.get(),
                HIJACKED_SERVICES.size(),
                ORIGINAL_SERVICES.size());
    }
}