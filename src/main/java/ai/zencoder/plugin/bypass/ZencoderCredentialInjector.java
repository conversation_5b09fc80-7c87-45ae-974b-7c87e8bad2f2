package ai.zencoder.plugin.bypass;

import ai.zencoder.plugin.auth.AuthInfo;
import ai.zencoder.plugin.auth.UserData;
import com.intellij.openapi.diagnostic.Logger;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Zencoder Credential Injector
 * Injects fake authentication credentials into Zencoder official plugin
 */
public class ZencoderCredentialInjector {
    
    private static final Logger LOG = Logger.getInstance(ZencoderCredentialInjector.class);
    
    private static final AtomicBoolean INJECTION_ACTIVE = new AtomicBoolean(false);
    
    // Current injected credentials
    private static volatile AuthInfo currentAuthInfo = null;
    private static volatile UserData currentUserData = null;
    private static volatile FakeTokenGenerator.FakeTokenSet currentTokenSet = null;
    
    /**
     * Inject complete fake credentials into Zencoder plugin
     */
    public static boolean injectFakeCredentials() {
        return injectFakeCredentials(FakeUserDataBuilder.buildCompleteProfile());
    }
    
    /**
     * Inject specific fake credentials into Zencoder plugin
     */
    public static boolean injectFakeCredentials(@NotNull FakeUserDataBuilder.FakeUserProfile profile) {
        try {
            LOG.info("Starting credential injection process...");
            
            boolean success = true;
            
            // Inject AuthInfo
            if (!injectAuthInfo(profile.authInfo)) {
                LOG.warn("Failed to inject AuthInfo");
                success = false;
            }
            
            // Inject UserData
            if (!injectUserData(profile.userData)) {
                LOG.warn("Failed to inject UserData");
                success = false;
            }
            
            // Inject tokens
            if (!injectTokens(profile.tokenSet)) {
                LOG.warn("Failed to inject tokens");
                success = false;
            }
            
            if (success) {
                currentAuthInfo = profile.authInfo;
                currentUserData = profile.userData;
                currentTokenSet = profile.tokenSet;
                INJECTION_ACTIVE.set(true);
                LOG.info("Credential injection completed successfully");
            } else {
                LOG.error("Credential injection partially failed");
            }
            
            return success;
            
        } catch (Exception e) {
            LOG.error("Failed to inject fake credentials", e);
            return false;
        }
    }
    
    /**
     * Inject AuthInfo into Zencoder plugin
     */
    public static boolean injectAuthInfo(@NotNull AuthInfo authInfo) {
        try {
            Object authService = ZencoderReflectionUtils.getAuthServiceInstance();
            if (authService == null) {
                LOG.warn("AuthService instance not found for AuthInfo injection");
                return false;
            }
            
            Class<?> authServiceClass = authService.getClass();
            boolean injected = false;
            
            // Try to find and inject AuthInfo field
            Field authInfoField = ZencoderReflectionUtils.findFieldByPatterns(
                authServiceClass, 
                new String[]{"authInfo", "currentAuth", "authentication", "auth"}
            );
            
            if (authInfoField != null) {
                if (ZencoderReflectionUtils.modifyField(authInfoField, authService, authInfo)) {
                    LOG.debug("Injected AuthInfo via field: " + authInfoField.getName());
                    injected = true;
                }
            }
            
            // Try to inject individual token fields
            if (!injected || true) { // Always try token injection as backup
                if (injectAccessToken(authInfo.getAccessToken())) {
                    LOG.debug("Injected access token");
                    injected = true;
                }
                
                if (authInfo.getRefreshToken() != null && 
                    ZencoderReflectionUtils.setRefreshToken(authInfo.getRefreshToken())) {
                    LOG.debug("Injected refresh token");
                    injected = true;
                }
            }
            
            // Try to call authentication methods
            if (tryCallAuthenticationMethods(authService, authInfo)) {
                LOG.debug("Called authentication methods");
                injected = true;
            }
            
            return injected;
            
        } catch (Exception e) {
            LOG.error("Failed to inject AuthInfo", e);
            return false;
        }
    }
    
    /**
     * Inject UserData into Zencoder plugin
     */
    public static boolean injectUserData(@NotNull UserData userData) {
        try {
            // Try AuthService first
            Object authService = ZencoderReflectionUtils.getAuthServiceInstance();
            boolean injected = false;
            
            if (authService != null) {
                Field userDataField = ZencoderReflectionUtils.findFieldByPatterns(
                    authService.getClass(),
                    new String[]{"userData", "user", "currentUser", "userInfo"}
                );
                
                if (userDataField != null) {
                    if (ZencoderReflectionUtils.modifyField(userDataField, authService, userData)) {
                        LOG.debug("Injected UserData into AuthService");
                        injected = true;
                    }
                }
            }
            
            // Try UserService
            Object userService = ZencoderReflectionUtils.getUserServiceInstance();
            if (userService != null) {
                Field userDataField = ZencoderReflectionUtils.findFieldByPatterns(
                    userService.getClass(),
                    new String[]{"userData", "user", "currentUser", "userInfo"}
                );
                
                if (userDataField != null) {
                    if (ZencoderReflectionUtils.modifyField(userDataField, userService, userData)) {
                        LOG.debug("Injected UserData into UserService");
                        injected = true;
                    }
                }
                
                // Try to call user-related methods
                if (tryCallUserMethods(userService, userData)) {
                    LOG.debug("Called user methods");
                    injected = true;
                }
            }
            
            return injected;
            
        } catch (Exception e) {
            LOG.error("Failed to inject UserData", e);
            return false;
        }
    }
    
    /**
     * Inject tokens into Zencoder plugin
     */
    public static boolean injectTokens(@NotNull FakeTokenGenerator.FakeTokenSet tokenSet) {
        try {
            boolean injected = false;
            
            // Inject access token
            if (ZencoderReflectionUtils.setAccessToken(tokenSet.accessToken)) {
                LOG.debug("Injected access token");
                injected = true;
            }
            
            // Inject refresh token
            if (ZencoderReflectionUtils.setRefreshToken(tokenSet.refreshToken)) {
                LOG.debug("Injected refresh token");
                injected = true;
            }
            
            // Try to inject API key and session ID
            if (injectApiKey(tokenSet.apiKey)) {
                LOG.debug("Injected API key");
                injected = true;
            }
            
            if (injectSessionId(tokenSet.sessionId)) {
                LOG.debug("Injected session ID");
                injected = true;
            }
            
            return injected;
            
        } catch (Exception e) {
            LOG.error("Failed to inject tokens", e);
            return false;
        }
    }
    
    /**
     * Inject access token
     */
    private static boolean injectAccessToken(@NotNull String accessToken) {
        return ZencoderReflectionUtils.setAccessToken(accessToken);
    }
    
    /**
     * Inject API key
     */
    private static boolean injectApiKey(@NotNull String apiKey) {
        try {
            Object authService = ZencoderReflectionUtils.getAuthServiceInstance();
            if (authService == null) {
                return false;
            }
            
            Field apiKeyField = ZencoderReflectionUtils.findFieldByPatterns(
                authService.getClass(),
                new String[]{"apiKey", "key", "clientKey", "authKey"}
            );
            
            if (apiKeyField != null) {
                return ZencoderReflectionUtils.modifyField(apiKeyField, authService, apiKey);
            }
            
            return false;
            
        } catch (Exception e) {
            LOG.debug("Failed to inject API key", e);
            return false;
        }
    }
    
    /**
     * Inject session ID
     */
    private static boolean injectSessionId(@NotNull String sessionId) {
        try {
            Object authService = ZencoderReflectionUtils.getAuthServiceInstance();
            if (authService == null) {
                return false;
            }
            
            Field sessionField = ZencoderReflectionUtils.findFieldByPatterns(
                authService.getClass(),
                new String[]{"sessionId", "session", "sessionToken", "sid"}
            );
            
            if (sessionField != null) {
                return ZencoderReflectionUtils.modifyField(sessionField, authService, sessionId);
            }
            
            return false;
            
        } catch (Exception e) {
            LOG.debug("Failed to inject session ID", e);
            return false;
        }
    }
    
    /**
     * Try to call authentication methods on the service
     */
    private static boolean tryCallAuthenticationMethods(@NotNull Object authService, @NotNull AuthInfo authInfo) {
        try {
            Class<?> serviceClass = authService.getClass();
            boolean called = false;
            
            // Try to find and call setAuthInfo method
            Method setAuthInfoMethod = ZencoderReflectionUtils.findMethod(serviceClass, "setAuthInfo", AuthInfo.class);
            if (setAuthInfoMethod != null) {
                ZencoderReflectionUtils.invokeMethod(authService, "setAuthInfo", authInfo);
                called = true;
            }
            
            // Try to find and call setAuthenticated method
            Method setAuthenticatedMethod = ZencoderReflectionUtils.findMethod(serviceClass, "setAuthenticated", boolean.class);
            if (setAuthenticatedMethod != null) {
                ZencoderReflectionUtils.invokeMethod(authService, "setAuthenticated", true);
                called = true;
            }
            
            // Try to find and call updateAuth method
            Method updateAuthMethod = ZencoderReflectionUtils.findMethod(serviceClass, "updateAuth", AuthInfo.class);
            if (updateAuthMethod != null) {
                ZencoderReflectionUtils.invokeMethod(authService, "updateAuth", authInfo);
                called = true;
            }
            
            return called;
            
        } catch (Exception e) {
            LOG.debug("Failed to call authentication methods", e);
            return false;
        }
    }
    
    /**
     * Try to call user-related methods on the service
     */
    private static boolean tryCallUserMethods(@NotNull Object userService, @NotNull UserData userData) {
        try {
            Class<?> serviceClass = userService.getClass();
            boolean called = false;
            
            // Try to find and call setUserData method
            Method setUserDataMethod = ZencoderReflectionUtils.findMethod(serviceClass, "setUserData", UserData.class);
            if (setUserDataMethod != null) {
                ZencoderReflectionUtils.invokeMethod(userService, "setUserData", userData);
                called = true;
            }
            
            // Try to find and call setCurrentUser method
            Method setCurrentUserMethod = ZencoderReflectionUtils.findMethod(serviceClass, "setCurrentUser", UserData.class);
            if (setCurrentUserMethod != null) {
                ZencoderReflectionUtils.invokeMethod(userService, "setCurrentUser", userData);
                called = true;
            }
            
            // Try to find and call updateUser method
            Method updateUserMethod = ZencoderReflectionUtils.findMethod(serviceClass, "updateUser", UserData.class);
            if (updateUserMethod != null) {
                ZencoderReflectionUtils.invokeMethod(userService, "updateUser", userData);
                called = true;
            }
            
            return called;
            
        } catch (Exception e) {
            LOG.debug("Failed to call user methods", e);
            return false;
        }
    }
    
    /**
     * Clear all injected credentials
     */
    public static boolean clearInjectedCredentials() {
        try {
            LOG.info("Clearing injected credentials...");
            
            boolean cleared = false;
            
            // Clear from AuthService
            Object authService = ZencoderReflectionUtils.getAuthServiceInstance();
            if (authService != null) {
                if (clearAuthServiceCredentials(authService)) {
                    cleared = true;
                }
            }
            
            // Clear from UserService
            Object userService = ZencoderReflectionUtils.getUserServiceInstance();
            if (userService != null) {
                if (clearUserServiceCredentials(userService)) {
                    cleared = true;
                }
            }
            
            // Reset internal state
            currentAuthInfo = null;
            currentUserData = null;
            currentTokenSet = null;
            INJECTION_ACTIVE.set(false);
            
            if (cleared) {
                LOG.info("Credentials cleared successfully");
            } else {
                LOG.warn("Failed to clear some credentials");
            }
            
            return cleared;
            
        } catch (Exception e) {
            LOG.error("Failed to clear injected credentials", e);
            return false;
        }
    }
    
    /**
     * Clear credentials from AuthService
     */
    private static boolean clearAuthServiceCredentials(@NotNull Object authService) {
        try {
            Class<?> serviceClass = authService.getClass();
            boolean cleared = false;
            
            // Clear AuthInfo field
            Field authInfoField = ZencoderReflectionUtils.findFieldByPatterns(
                serviceClass,
                new String[]{"authInfo", "currentAuth", "authentication", "auth"}
            );
            if (authInfoField != null) {
                ZencoderReflectionUtils.modifyField(authInfoField, authService, null);
                cleared = true;
            }
            
            // Clear token fields
            Field tokenField = ZencoderReflectionUtils.findFieldByPatterns(
                serviceClass,
                new String[]{"accessToken", "token", "authToken"}
            );
            if (tokenField != null) {
                ZencoderReflectionUtils.modifyField(tokenField, authService, null);
                cleared = true;
            }
            
            // Call reset methods
            Method resetMethod = ZencoderReflectionUtils.findMethod(serviceClass, "reset");
            if (resetMethod != null) {
                ZencoderReflectionUtils.invokeMethod(authService, "reset");
                cleared = true;
            }
            
            Method clearMethod = ZencoderReflectionUtils.findMethod(serviceClass, "clear");
            if (clearMethod != null) {
                ZencoderReflectionUtils.invokeMethod(authService, "clear");
                cleared = true;
            }
            
            return cleared;
            
        } catch (Exception e) {
            LOG.debug("Failed to clear AuthService credentials", e);
            return false;
        }
    }
    
    /**
     * Clear credentials from UserService
     */
    private static boolean clearUserServiceCredentials(@NotNull Object userService) {
        try {
            Class<?> serviceClass = userService.getClass();
            boolean cleared = false;
            
            // Clear UserData field
            Field userDataField = ZencoderReflectionUtils.findFieldByPatterns(
                serviceClass,
                new String[]{"userData", "user", "currentUser", "userInfo"}
            );
            if (userDataField != null) {
                ZencoderReflectionUtils.modifyField(userDataField, userService, null);
                cleared = true;
            }
            
            // Call clear methods
            Method clearUserMethod = ZencoderReflectionUtils.findMethod(serviceClass, "clearUser");
            if (clearUserMethod != null) {
                ZencoderReflectionUtils.invokeMethod(userService, "clearUser");
                cleared = true;
            }
            
            return cleared;
            
        } catch (Exception e) {
            LOG.debug("Failed to clear UserService credentials", e);
            return false;
        }
    }
    
    /**
     * Check if credential injection is currently active
     */
    public static boolean isInjectionActive() {
        return INJECTION_ACTIVE.get();
    }
    
    /**
     * Get current injected AuthInfo
     */
    @Nullable
    public static AuthInfo getCurrentAuthInfo() {
        return currentAuthInfo;
    }
    
    /**
     * Get current injected UserData
     */
    @Nullable
    public static UserData getCurrentUserData() {
        return currentUserData;
    }
    
    /**
     * Get current injected token set
     */
    @Nullable
    public static FakeTokenGenerator.FakeTokenSet getCurrentTokenSet() {
        return currentTokenSet;
    }
    
    /**
     * Refresh injected credentials with new tokens
     */
    public static boolean refreshCredentials() {
        if (!isInjectionActive()) {
            LOG.warn("No active injection to refresh");
            return false;
        }
        
        try {
            // Generate new tokens for the same user
            String userId = currentUserData != null ? currentUserData.getUserId() : 
                           FakeTokenGenerator.generateFakeUserId();
            
            FakeUserDataBuilder.FakeUserProfile newProfile = FakeUserDataBuilder.buildCompleteProfile(userId);
            
            return injectFakeCredentials(newProfile);
            
        } catch (Exception e) {
            LOG.error("Failed to refresh credentials", e);
            return false;
        }
    }
}