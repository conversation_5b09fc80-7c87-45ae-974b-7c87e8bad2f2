package ai.zencoder.plugin.bypass;

import ai.zencoder.plugin.auth.AuthInfo;
import ai.zencoder.plugin.auth.UserData;
import com.intellij.openapi.diagnostic.Logger;
import org.jetbrains.annotations.NotNull;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * Fake User Data Builder
 * Constructs realistic fake user data and authentication info for Zencoder bypass
 */
public class FakeUserDataBuilder {
    
    private static final Logger LOG = Logger.getInstance(FakeUserDataBuilder.class);
    
    // VIP plan names
    private static final List<String> VIP_PLANS = Arrays.asList(
        "premium", "enterprise", "pro", "business", "unlimited"
    );
    
    // All possible permissions for VIP users
    private static final List<String> ALL_PERMISSIONS = Arrays.asList(
        "chat",
        "code_generation", 
        "code_completion",
        "multi_repo",
        "advanced_features",
        "unlimited_requests",
        "priority_support",
        "custom_models",
        "team_collaboration",
        "analytics",
        "api_access",
        "export_data",
        "admin_panel"
    );
    
    // Fake user names for development
    private static final List<String> FAKE_DISPLAY_NAMES = Arrays.asList(
        "Development User",
        "Test Developer", 
        "QA Engineer",
        "Senior Developer",
        "Tech Lead",
        "DevOps Engineer",
        "Full Stack Developer"
    );
    
    // Fake email domains
    private static final List<String> FAKE_EMAIL_DOMAINS = Arrays.asList(
        "example.com",
        "test.dev",
        "localhost.dev",
        "dev.local",
        "staging.test"
    );
    
    /**
     * Build a VIP user with all premium features
     */
    @NotNull
    public static UserData buildVipUser() {
        return buildVipUser(FakeTokenGenerator.generateFakeUserId());
    }
    
    /**
     * Build a VIP user with specific user ID
     */
    @NotNull
    public static UserData buildVipUser(@NotNull String userId) {
        try {
            String displayName = getRandomElement(FAKE_DISPLAY_NAMES);
            String email = generateFakeEmail();
            
            // Create VIP plans (multiple for enterprise users)
            List<String> plans = new ArrayList<>();
            plans.add("enterprise");
            plans.add("premium");
            
            // Grant all permissions
            List<String> permissions = new ArrayList<>(ALL_PERMISSIONS);
            
            // Create custom claims for VIP status
            Map<String, Object> customClaims = new HashMap<>();
            customClaims.put("subscription_status", "active");
            customClaims.put("plan_type", "enterprise");
            customClaims.put("expires_at", System.currentTimeMillis() + TimeUnit.DAYS.toMillis(365));
            customClaims.put("max_requests_per_day", -1); // Unlimited
            customClaims.put("max_tokens_per_request", -1); // Unlimited
            customClaims.put("priority_queue", true);
            customClaims.put("beta_features", true);
            customClaims.put("custom_models", true);
            customClaims.put("team_size", 100);
            customClaims.put("api_rate_limit", -1); // Unlimited
            
            UserData userData = new UserData(userId, displayName, email, plans, permissions, customClaims);
            
            LOG.info("Built VIP user data: " + userData.getDisplayName() + " (" + userData.getEmail() + ")");
            return userData;
            
        } catch (Exception e) {
            LOG.error("Failed to build VIP user data", e);
            // Fallback to minimal VIP user
            return buildMinimalVipUser(userId);
        }
    }
    
    /**
     * Build a minimal VIP user (fallback)
     */
    @NotNull
    public static UserData buildMinimalVipUser(@NotNull String userId) {
        return new UserData(
            userId,
            "Development User",
            "<EMAIL>",
            Arrays.asList("premium"),
            Arrays.asList("chat", "code_generation", "multi_repo"),
            Map.of("subscription_status", "active", "plan_type", "premium")
        );
    }
    
    /**
     * Build fake AuthInfo with realistic tokens
     */
    @NotNull
    public static AuthInfo buildFakeAuthInfo() {
        return buildFakeAuthInfo(FakeTokenGenerator.generateFakeUserId());
    }
    
    /**
     * Build fake AuthInfo for specific user
     */
    @NotNull
    public static AuthInfo buildFakeAuthInfo(@NotNull String userId) {
        try {
            String accessToken = FakeTokenGenerator.generateFakeAccessToken(userId);
            String refreshToken = FakeTokenGenerator.generateFakeRefreshToken();
            long expiresAt = System.currentTimeMillis() + TimeUnit.HOURS.toMillis(24);
            
            AuthInfo authInfo = new AuthInfo(accessToken, refreshToken, expiresAt);
            
            LOG.debug("Built fake AuthInfo for user: " + userId);
            return authInfo;
            
        } catch (Exception e) {
            LOG.error("Failed to build fake AuthInfo", e);
            // Fallback to minimal auth info
            return new AuthInfo("fake-token-" + UUID.randomUUID(), "fake-refresh-" + UUID.randomUUID());
        }
    }
    
    /**
     * Build enterprise user with maximum privileges
     */
    @NotNull
    public static UserData buildEnterpriseUser() {
        return buildEnterpriseUser(FakeTokenGenerator.generateFakeUserId());
    }
    
    /**
     * Build enterprise user with specific user ID
     */
    @NotNull
    public static UserData buildEnterpriseUser(@NotNull String userId) {
        try {
            String displayName = "Enterprise " + getRandomElement(FAKE_DISPLAY_NAMES);
            String email = generateFakeEmail();
            
            // Enterprise plans
            List<String> plans = Arrays.asList("enterprise", "premium", "business", "unlimited");
            
            // All permissions plus enterprise-specific ones
            List<String> permissions = new ArrayList<>(ALL_PERMISSIONS);
            permissions.addAll(Arrays.asList(
                "enterprise_features",
                "white_label",
                "custom_deployment",
                "dedicated_support",
                "sla_guarantee",
                "audit_logs",
                "compliance_features"
            ));
            
            // Enterprise custom claims
            Map<String, Object> customClaims = new HashMap<>();
            customClaims.put("subscription_status", "active");
            customClaims.put("plan_type", "enterprise");
            customClaims.put("expires_at", System.currentTimeMillis() + TimeUnit.DAYS.toMillis(365 * 2)); // 2 years
            customClaims.put("max_requests_per_day", -1);
            customClaims.put("max_tokens_per_request", -1);
            customClaims.put("priority_queue", true);
            customClaims.put("beta_features", true);
            customClaims.put("custom_models", true);
            customClaims.put("team_size", -1); // Unlimited
            customClaims.put("api_rate_limit", -1);
            customClaims.put("white_label", true);
            customClaims.put("dedicated_support", true);
            customClaims.put("sla_level", "enterprise");
            customClaims.put("compliance_certified", true);
            
            UserData userData = new UserData(userId, displayName, email, plans, permissions, customClaims);
            
            LOG.info("Built Enterprise user data: " + userData.getDisplayName());
            return userData;
            
        } catch (Exception e) {
            LOG.error("Failed to build Enterprise user data", e);
            return buildVipUser(userId); // Fallback to VIP
        }
    }
    
    /**
     * Build a complete fake user profile with tokens
     */
    @NotNull
    public static FakeUserProfile buildCompleteProfile() {
        return buildCompleteProfile(FakeTokenGenerator.generateFakeUserId());
    }
    
    /**
     * Build a complete fake user profile for specific user ID
     */
    @NotNull
    public static FakeUserProfile buildCompleteProfile(@NotNull String userId) {
        UserData userData = buildEnterpriseUser(userId);
        AuthInfo authInfo = buildFakeAuthInfo(userId);
        FakeTokenGenerator.FakeTokenSet tokenSet = FakeTokenGenerator.generateFakeTokenSet(userId);
        
        return new FakeUserProfile(userData, authInfo, tokenSet);
    }
    
    /**
     * Generate a fake email address
     */
    @NotNull
    private static String generateFakeEmail() {
        String username = "dev" + (int)(Math.random() * 1000);
        String domain = getRandomElement(FAKE_EMAIL_DOMAINS);
        return username + "@" + domain;
    }
    
    /**
     * Get random element from list
     */
    @NotNull
    private static <T> T getRandomElement(@NotNull List<T> list) {
        return list.get((int)(Math.random() * list.size()));
    }
    
    /**
     * Check if user data represents a VIP user
     */
    public static boolean isVipUserData(@NotNull UserData userData) {
        // Check if user has any VIP plans
        for (String plan : userData.getPlans()) {
            if (VIP_PLANS.contains(plan.toLowerCase())) {
                return true;
            }
        }
        
        // Check custom claims
        Map<String, Object> claims = userData.getCustomClaims();
        if (claims != null) {
            Object status = claims.get("subscription_status");
            if ("active".equals(status)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Upgrade user data to VIP status
     */
    @NotNull
    public static UserData upgradeToVip(@NotNull UserData originalUser) {
        List<String> newPlans = new ArrayList<>(originalUser.getPlans());
        if (!newPlans.contains("premium")) {
            newPlans.add("premium");
        }
        
        List<String> newPermissions = new ArrayList<>(originalUser.getPermissions());
        for (String permission : ALL_PERMISSIONS) {
            if (!newPermissions.contains(permission)) {
                newPermissions.add(permission);
            }
        }
        
        Map<String, Object> newClaims = new HashMap<>();
        if (originalUser.getCustomClaims() != null) {
            newClaims.putAll(originalUser.getCustomClaims());
        }
        newClaims.put("subscription_status", "active");
        newClaims.put("plan_type", "premium");
        newClaims.put("expires_at", System.currentTimeMillis() + TimeUnit.DAYS.toMillis(365));
        
        return new UserData(
            originalUser.getUserId(),
            originalUser.getDisplayName(),
            originalUser.getEmail(),
            newPlans,
            newPermissions,
            newClaims
        );
    }
    
    /**
     * Container for complete fake user profile
     */
    public static class FakeUserProfile {
        public final UserData userData;
        public final AuthInfo authInfo;
        public final FakeTokenGenerator.FakeTokenSet tokenSet;
        
        public FakeUserProfile(@NotNull UserData userData, @NotNull AuthInfo authInfo, 
                              @NotNull FakeTokenGenerator.FakeTokenSet tokenSet) {
            this.userData = userData;
            this.authInfo = authInfo;
            this.tokenSet = tokenSet;
        }
        
        @Override
        public String toString() {
            return String.format("FakeUserProfile{user='%s', email='%s', plans=%s, permissions=%d}",
                    userData.getDisplayName(),
                    userData.getEmail(),
                    userData.getPlans(),
                    userData.getPermissions().size());
        }
    }
}