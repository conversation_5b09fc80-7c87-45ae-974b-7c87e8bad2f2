package ai.zencoder.plugin.bypass;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.Arrays;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * Zencoder Reflection Utilities
 * Core reflection-based bypass system for Zencoder authentication
 */
public class ZencoderReflectionUtils {
    
    private static final Logger LOG = Logger.getInstance(ZencoderReflectionUtils.class);
    
    // Target class patterns for Zencoder official plugin
    private static final String[] ZENCODER_AUTH_SERVICE_PATTERNS = {
        "ai.zencoder.plugin.auth.AuthService",
        "ai.zencoder.plugin.auth.AuthServiceImpl",
        "ai.zencoder.plugin.auth.DefaultAuthService",
        "ai.zencoder.plugin.service.AuthenticationService",
        "com.zencoder.auth.AuthService",
        "com.zencoder.auth.AuthServiceImpl"
    };
    
    private static final String[] ZENCODER_USER_SERVICE_PATTERNS = {
        "ai.zencoder.plugin.user.UserService",
        "ai.zencoder.plugin.user.UserServiceImpl",
        "ai.zencoder.plugin.service.UserService",
        "com.zencoder.user.UserService"
    };
    
    private static final String[] ZENCODER_AUTH_MANAGER_PATTERNS = {
        "ai.zencoder.plugin.auth.AuthManager",
        "ai.zencoder.plugin.auth.AuthenticationManager",
        "com.zencoder.auth.AuthManager"
    };
    
    // Field name patterns to search for
    private static final String[] ACCESS_TOKEN_FIELD_PATTERNS = {
        "accessToken", "token", "authToken", "bearerToken", "jwt"
    };
    
    private static final String[] REFRESH_TOKEN_FIELD_PATTERNS = {
        "refreshToken", "refresh", "renewToken"
    };
    
    private static final String[] USER_DATA_FIELD_PATTERNS = {
        "userData", "user", "userInfo", "currentUser", "authenticatedUser"
    };
    
    private static final String[] AUTH_STATE_FIELD_PATTERNS = {
        "authState", "authenticated", "isAuthenticated", "authInfo", "authData"
    };
    
    // Caches for reflection objects
    private static final ConcurrentMap<String, Class<?>> CLASS_CACHE = new ConcurrentHashMap<>();
    private static final ConcurrentMap<String, Field> FIELD_CACHE = new ConcurrentHashMap<>();
    private static final ConcurrentMap<String, Method> METHOD_CACHE = new ConcurrentHashMap<>();
    private static final ConcurrentMap<String, Object> INSTANCE_CACHE = new ConcurrentHashMap<>();
    
    /**
     * Get access token from Zencoder official plugin
     */
    @Nullable
    public static String getAccessToken() {
        try {
            Object authService = getAuthServiceInstance();
            if (authService == null) {
                LOG.warn("Failed to get AuthService instance");
                return null;
            }
            
            Field tokenField = findFieldByPatterns(authService.getClass(), ACCESS_TOKEN_FIELD_PATTERNS);
            if (tokenField != null) {
                tokenField.setAccessible(true);
                Object token = tokenField.get(authService);
                return token != null ? token.toString() : null;
            }
            
            LOG.warn("No access token field found in AuthService");
            return null;
            
        } catch (Exception e) {
            LOG.error("Failed to get access token via reflection", e);
            return null;
        }
    }
    
    /**
     * Set access token in Zencoder official plugin
     */
    public static boolean setAccessToken(@NotNull String newToken) {
        try {
            Object authService = getAuthServiceInstance();
            if (authService == null) {
                LOG.warn("Failed to get AuthService instance for token injection");
                return false;
            }
            
            Field tokenField = findFieldByPatterns(authService.getClass(), ACCESS_TOKEN_FIELD_PATTERNS);
            if (tokenField != null) {
                return modifyField(tokenField, authService, newToken);
            }
            
            LOG.warn("No access token field found for injection");
            return false;
            
        } catch (Exception e) {
            LOG.error("Failed to set access token via reflection", e);
            return false;
        }
    }
    
    /**
     * Set refresh token in Zencoder official plugin
     */
    public static boolean setRefreshToken(@NotNull String newRefreshToken) {
        try {
            Object authService = getAuthServiceInstance();
            if (authService == null) {
                return false;
            }
            
            Field refreshField = findFieldByPatterns(authService.getClass(), REFRESH_TOKEN_FIELD_PATTERNS);
            if (refreshField != null) {
                return modifyField(refreshField, authService, newRefreshToken);
            }
            
            return false;
            
        } catch (Exception e) {
            LOG.error("Failed to set refresh token via reflection", e);
            return false;
        }
    }
    
    /**
     * Get AuthService instance from Zencoder official plugin
     */
    @Nullable
    public static Object getAuthServiceInstance() {
        String cacheKey = "authService";
        Object cached = INSTANCE_CACHE.get(cacheKey);
        if (cached != null) {
            return cached;
        }
        
        try {
            // Try to get service from IntelliJ service manager
            for (String pattern : ZENCODER_AUTH_SERVICE_PATTERNS) {
                try {
                    Class<?> serviceClass = findClass(pattern);
                    if (serviceClass != null) {
                        Object instance = ApplicationManager.getApplication().getService(serviceClass);
                        if (instance != null) {
                            INSTANCE_CACHE.put(cacheKey, instance);
                            LOG.info("Found AuthService instance: " + serviceClass.getName());
                            return instance;
                        }
                    }
                } catch (Exception e) {
                    LOG.debug("Failed to get service for pattern: " + pattern, e);
                }
            }
            
            LOG.warn("No AuthService instance found");
            return null;
            
        } catch (Exception e) {
            LOG.error("Failed to get AuthService instance", e);
            return null;
        }
    }
    
    /**
     * Get UserService instance from Zencoder official plugin
     */
    @Nullable
    public static Object getUserServiceInstance() {
        String cacheKey = "userService";
        Object cached = INSTANCE_CACHE.get(cacheKey);
        if (cached != null) {
            return cached;
        }
        
        try {
            for (String pattern : ZENCODER_USER_SERVICE_PATTERNS) {
                try {
                    Class<?> serviceClass = findClass(pattern);
                    if (serviceClass != null) {
                        Object instance = ApplicationManager.getApplication().getService(serviceClass);
                        if (instance != null) {
                            INSTANCE_CACHE.put(cacheKey, instance);
                            LOG.info("Found UserService instance: " + serviceClass.getName());
                            return instance;
                        }
                    }
                } catch (Exception e) {
                    LOG.debug("Failed to get user service for pattern: " + pattern, e);
                }
            }
            
            return null;
            
        } catch (Exception e) {
            LOG.error("Failed to get UserService instance", e);
            return null;
        }
    }
    
    /**
     * Find class by name with caching
     */
    @Nullable
    public static Class<?> findClass(@NotNull String className) {
        Class<?> cached = CLASS_CACHE.get(className);
        if (cached != null) {
            return cached;
        }
        
        try {
            Class<?> clazz = Class.forName(className);
            CLASS_CACHE.put(className, clazz);
            return clazz;
        } catch (ClassNotFoundException e) {
            LOG.debug("Class not found: " + className);
            return null;
        }
    }
    
    /**
     * Find field by name patterns
     */
    @Nullable
    public static Field findFieldByPatterns(@NotNull Class<?> clazz, @NotNull String[] patterns) {
        String cacheKey = clazz.getName() + ":" + Arrays.toString(patterns);
        Field cached = FIELD_CACHE.get(cacheKey);
        if (cached != null) {
            return cached;
        }
        
        try {
            // Search in declared fields first
            Field[] fields = clazz.getDeclaredFields();
            for (String pattern : patterns) {
                for (Field field : fields) {
                    if (field.getName().equals(pattern) || 
                        field.getName().toLowerCase().contains(pattern.toLowerCase())) {
                        FIELD_CACHE.put(cacheKey, field);
                        return field;
                    }
                }
            }
            
            // Search in parent classes
            Class<?> superClass = clazz.getSuperclass();
            if (superClass != null && superClass != Object.class) {
                Field field = findFieldByPatterns(superClass, patterns);
                if (field != null) {
                    FIELD_CACHE.put(cacheKey, field);
                    return field;
                }
            }
            
            return null;
            
        } catch (Exception e) {
            LOG.error("Failed to find field by patterns", e);
            return null;
        }
    }
    
    /**
     * Modify field value with advanced reflection techniques
     */
    public static boolean modifyField(@NotNull Field field, @NotNull Object target, @Nullable Object newValue) {
        try {
            field.setAccessible(true);
            
            // Handle final fields
            if (Modifier.isFinal(field.getModifiers())) {
                return modifyFinalField(field, target, newValue);
            } else {
                field.set(target, newValue);
                LOG.debug("Successfully modified field: " + field.getName());
                return true;
            }
            
        } catch (Exception e) {
            LOG.error("Failed to modify field: " + field.getName(), e);
            return false;
        }
    }
    
    /**
     * Modify final field using advanced reflection techniques
     */
    public static boolean modifyFinalField(@NotNull Field field, @NotNull Object target, @Nullable Object newValue) {
        try {
            field.setAccessible(true);
            
            // Method 1: Try to remove final modifier
            try {
                Field modifiersField = Field.class.getDeclaredField("modifiers");
                modifiersField.setAccessible(true);
                modifiersField.setInt(field, field.getModifiers() & ~Modifier.FINAL);
                field.set(target, newValue);
                LOG.debug("Successfully modified final field using modifiers: " + field.getName());
                return true;
            } catch (Exception e) {
                LOG.debug("Failed to modify final field using modifiers", e);
            }
            
            // Method 2: Try using Unsafe class
            try {
                Class<?> unsafeClass = Class.forName("sun.misc.Unsafe");
                Field unsafeField = unsafeClass.getDeclaredField("theUnsafe");
                unsafeField.setAccessible(true);
                Object unsafe = unsafeField.get(null);
                
                Method putObjectMethod = unsafeClass.getMethod("putObject", Object.class, long.class, Object.class);
                Method objectFieldOffsetMethod = unsafeClass.getMethod("objectFieldOffset", Field.class);
                
                long offset = (Long) objectFieldOffsetMethod.invoke(unsafe, field);
                putObjectMethod.invoke(unsafe, target, offset, newValue);
                
                LOG.debug("Successfully modified final field using Unsafe: " + field.getName());
                return true;
            } catch (Exception e) {
                LOG.debug("Failed to modify final field using Unsafe", e);
            }
            
            // Method 3: Try direct field access (for some JVM implementations)
            try {
                field.set(target, newValue);
                LOG.debug("Successfully modified final field with direct access: " + field.getName());
                return true;
            } catch (Exception e) {
                LOG.debug("Failed to modify final field with direct access", e);
            }
            
            LOG.warn("All methods failed to modify final field: " + field.getName());
            return false;
            
        } catch (Exception e) {
            LOG.error("Failed to modify final field: " + field.getName(), e);
            return false;
        }
    }
    
    /**
     * Find method by name and parameter types
     */
    @Nullable
    public static Method findMethod(@NotNull Class<?> clazz, @NotNull String methodName, Class<?>... parameterTypes) {
        String cacheKey = clazz.getName() + ":" + methodName + ":" + Arrays.toString(parameterTypes);
        Method cached = METHOD_CACHE.get(cacheKey);
        if (cached != null) {
            return cached;
        }
        
        try {
            Method method = clazz.getDeclaredMethod(methodName, parameterTypes);
            method.setAccessible(true);
            METHOD_CACHE.put(cacheKey, method);
            return method;
        } catch (NoSuchMethodException e) {
            // Try in parent classes
            Class<?> superClass = clazz.getSuperclass();
            if (superClass != null && superClass != Object.class) {
                Method method = findMethod(superClass, methodName, parameterTypes);
                if (method != null) {
                    METHOD_CACHE.put(cacheKey, method);
                    return method;
                }
            }
            
            LOG.debug("Method not found: " + methodName + " in " + clazz.getName());
            return null;
        }
    }
    
    /**
     * Invoke method with reflection
     */
    @Nullable
    public static Object invokeMethod(@NotNull Object target, @NotNull String methodName, Object... args) {
        try {
            Class<?>[] paramTypes = new Class[args.length];
            for (int i = 0; i < args.length; i++) {
                paramTypes[i] = args[i] != null ? args[i].getClass() : Object.class;
            }
            
            Method method = findMethod(target.getClass(), methodName, paramTypes);
            if (method != null) {
                return method.invoke(target, args);
            }
            
            LOG.warn("Method not found for invocation: " + methodName);
            return null;
            
        } catch (Exception e) {
            LOG.error("Failed to invoke method: " + methodName, e);
            return null;
        }
    }
    
    /**
     * Clear all caches
     */
    public static void clearCaches() {
        CLASS_CACHE.clear();
        FIELD_CACHE.clear();
        METHOD_CACHE.clear();
        INSTANCE_CACHE.clear();
        LOG.info("Reflection caches cleared");
    }
    
    /**
     * Get cache statistics for debugging
     */
    @NotNull
    public static String getCacheStats() {
        return String.format("ReflectionCache[classes=%d, fields=%d, methods=%d, instances=%d]",
                CLASS_CACHE.size(), FIELD_CACHE.size(), METHOD_CACHE.size(), INSTANCE_CACHE.size());
    }
}