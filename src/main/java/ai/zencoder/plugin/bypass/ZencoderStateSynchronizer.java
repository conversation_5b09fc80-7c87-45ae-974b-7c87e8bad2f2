package ai.zencoder.plugin.bypass;

import ai.zencoder.plugin.auth.AuthInfo;
import ai.zencoder.plugin.auth.UserData;
import ai.zencoder.plugin.noauth.SecureNoAuthConfigManager;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.util.Alarm;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Zencoder State Synchronizer
 * Maintains consistency and persistence of authentication bypass state
 */
public class ZencoderStateSynchronizer {
    
    private static final Logger LOG = Logger.getInstance(ZencoderStateSynchronizer.class);
    
    // Synchronization intervals
    private static final int SYNC_INTERVAL_MS = 30000; // 30 seconds
    private static final int CONFLICT_CHECK_INTERVAL_MS = 10000; // 10 seconds
    
    // State management
    private static volatile boolean authBypassActive = false;
    private static volatile AuthInfo currentAuthInfo = null;
    private static volatile UserData currentUserData = null;
    private static volatile FakeTokenGenerator.FakeTokenSet currentTokenSet = null;
    
    // Monitoring
    private static final AtomicBoolean monitoringActive = new AtomicBoolean(false);
    private static final AtomicLong lastSyncTime = new AtomicLong(0);
    private static final AtomicLong lastConflictCheckTime = new AtomicLong(0);
    
    // Alarm for periodic tasks
    private static final Alarm syncAlarm = new Alarm(Alarm.ThreadToUse.POOLED_THREAD);
    private static final Alarm conflictAlarm = new Alarm(Alarm.ThreadToUse.POOLED_THREAD);
    
    /**
     * Start state synchronization and monitoring
     */
    public static void startStateSynchronization() {
        if (monitoringActive.compareAndSet(false, true)) {
            LOG.info("Starting Zencoder state synchronization...");
            
            // Initial synchronization
            synchronizeAuthState();
            
            // Schedule periodic synchronization
            schedulePeriodicSync();
            scheduleConflictChecking();
            
            LOG.info("State synchronization started successfully");
        } else {
            LOG.debug("State synchronization already active");
        }
    }
    
    /**
     * Stop state synchronization and monitoring
     */
    public static void stopStateSynchronization() {
        if (monitoringActive.compareAndSet(true, false)) {
            LOG.info("Stopping Zencoder state synchronization...");
            
            // Cancel scheduled tasks
            syncAlarm.cancelAllRequests();
            conflictAlarm.cancelAllRequests();
            
            LOG.info("State synchronization stopped");
        } else {
            LOG.debug("State synchronization already inactive");
        }
    }
    
    /**
     * Synchronize authentication state across all components
     */
    public static void synchronizeAuthState() {
        try {
            LOG.debug("Synchronizing authentication state...");
            
            // Get current state from injector
            AuthInfo injectedAuth = ZencoderCredentialInjector.getCurrentAuthInfo();
            UserData injectedUser = ZencoderCredentialInjector.getCurrentUserData();
            FakeTokenGenerator.FakeTokenSet injectedTokens = ZencoderCredentialInjector.getCurrentTokenSet();
            
            // Update local state
            if (injectedAuth != null) {
                currentAuthInfo = injectedAuth;
                authBypassActive = true;
            }
            
            if (injectedUser != null) {
                currentUserData = injectedUser;
            }
            
            if (injectedTokens != null) {
                currentTokenSet = injectedTokens;
            }
            
            // Persist state
            persistAuthState();
            
            // Verify synchronization
            if (verifyStateSynchronization()) {
                LOG.debug("State synchronization completed successfully");
            } else {
                LOG.warn("State synchronization verification failed");
                handleSyncFailure();
            }
            
            lastSyncTime.set(System.currentTimeMillis());
            
        } catch (Exception e) {
            LOG.error("Failed to synchronize authentication state", e);
            handleSyncFailure();
        }
    }
    
    /**
     * Persist authentication state to storage
     */
    public static void persistAuthState() {
        try {
            SecureNoAuthConfigManager configManager = SecureNoAuthConfigManager.getInstance();
            
            if (currentAuthInfo != null) {
                // Store auth info securely
                configManager.setBypassEnabled(true);
                configManager.setLastBypassTime(System.currentTimeMillis());
                
                if (currentUserData != null) {
                    configManager.setFakeUserId(currentUserData.getUserId());
                    configManager.setFakeUserEmail(currentUserData.getEmail());
                    configManager.setFakeUserName(currentUserData.getDisplayName());
                }
                
                LOG.debug("Authentication state persisted successfully");
            }
            
        } catch (Exception e) {
            LOG.error("Failed to persist authentication state", e);
        }
    }
    
    /**
     * Load authentication state from storage
     */
    public static void loadAuthState() {
        try {
            SecureNoAuthConfigManager configManager = SecureNoAuthConfigManager.getInstance();
            
            if (configManager.isBypassEnabled()) {
                LOG.info("Loading persisted authentication state...");
                
                // Restore bypass state
                String userId = configManager.getFakeUserId();
                String userEmail = configManager.getFakeUserEmail();
                String userName = configManager.getFakeUserName();
                
                if (userId != null && !userId.isEmpty()) {
                    // Recreate user profile
                    FakeUserDataBuilder.FakeUserProfile profile = FakeUserDataBuilder.buildCompleteProfile(userId);
                    
                    // Update display name and email if available
                    if (userName != null && userEmail != null) {
                        UserData customUser = new UserData(
                            userId, userName, userEmail,
                            profile.userData.getPlans(),
                            profile.userData.getPermissions(),
                            profile.userData.getCustomClaims()
                        );
                        profile = new FakeUserDataBuilder.FakeUserProfile(customUser, profile.authInfo, profile.tokenSet);
                    }
                    
                    // Inject credentials
                    if (ZencoderCredentialInjector.injectFakeCredentials(profile)) {
                        currentAuthInfo = profile.authInfo;
                        currentUserData = profile.userData;
                        currentTokenSet = profile.tokenSet;
                        authBypassActive = true;
                        
                        LOG.info("Authentication state loaded and restored successfully");
                    } else {
                        LOG.warn("Failed to restore authentication state");
                    }
                }
            } else {
                LOG.debug("No persisted authentication state found");
            }
            
        } catch (Exception e) {
            LOG.error("Failed to load authentication state", e);
        }
    }
    
    /**
     * Detect and resolve authentication conflicts
     */
    public static boolean detectAuthConflict() {
        try {
            if (!authBypassActive) {
                return false; // No bypass active, no conflict possible
            }
            
            // Check if Zencoder official plugin has overridden our state
            String currentToken = ZencoderReflectionUtils.getAccessToken();
            
            if (currentToken == null) {
                LOG.warn("Access token is null, possible conflict detected");
                return true;
            }
            
            if (currentAuthInfo != null && !currentToken.equals(currentAuthInfo.getAccessToken())) {
                LOG.warn("Access token mismatch detected, conflict present");
                return true;
            }
            
            // Check service hijacking status
            if (!ZencoderServiceHijacker.isHijackActive()) {
                LOG.warn("Service hijacking is inactive, possible conflict");
                return true;
            }
            
            // Check credential injection status
            if (!ZencoderCredentialInjector.isInjectionActive()) {
                LOG.warn("Credential injection is inactive, possible conflict");
                return true;
            }
            
            return false; // No conflicts detected
            
        } catch (Exception e) {
            LOG.error("Failed to detect authentication conflicts", e);
            return true; // Assume conflict on error
        }
    }
    
    /**
     * Resolve authentication conflicts
     */
    public static void resolveAuthConflict() {
        try {
            LOG.info("Resolving authentication conflicts...");
            
            // Re-inject credentials
            if (currentAuthInfo != null && currentUserData != null && currentTokenSet != null) {
                FakeUserDataBuilder.FakeUserProfile profile = new FakeUserDataBuilder.FakeUserProfile(
                    currentUserData, currentAuthInfo, currentTokenSet
                );
                
                if (ZencoderCredentialInjector.injectFakeCredentials(profile)) {
                    LOG.info("Credentials re-injected successfully");
                } else {
                    LOG.warn("Failed to re-inject credentials");
                }
            }
            
            // Re-hijack services if needed
            if (!ZencoderServiceHijacker.isHijackActive()) {
                if (ZencoderServiceHijacker.hijackAllServices()) {
                    LOG.info("Services re-hijacked successfully");
                } else {
                    LOG.warn("Failed to re-hijack services");
                }
            }
            
            // Refresh tokens if they're expired
            if (currentAuthInfo != null && currentAuthInfo.isExpired()) {
                if (ZencoderCredentialInjector.refreshCredentials()) {
                    LOG.info("Credentials refreshed successfully");
                    // Update local state
                    currentAuthInfo = ZencoderCredentialInjector.getCurrentAuthInfo();
                    currentTokenSet = ZencoderCredentialInjector.getCurrentTokenSet();
                }
            }
            
            LOG.info("Authentication conflict resolution completed");
            
        } catch (Exception e) {
            LOG.error("Failed to resolve authentication conflicts", e);
        }
    }
    
    /**
     * Verify that state synchronization was successful
     */
    private static boolean verifyStateSynchronization() {
        try {
            // Check if injected credentials match local state
            AuthInfo injectedAuth = ZencoderCredentialInjector.getCurrentAuthInfo();
            if (currentAuthInfo != null && injectedAuth != null) {
                if (!currentAuthInfo.getAccessToken().equals(injectedAuth.getAccessToken())) {
                    return false;
                }
            }
            
            // Check if services are properly hijacked
            if (authBypassActive && !ZencoderServiceHijacker.isHijackActive()) {
                return false;
            }
            
            // Check if tokens are accessible via reflection
            String reflectedToken = ZencoderReflectionUtils.getAccessToken();
            if (currentAuthInfo != null && reflectedToken != null) {
                if (!currentAuthInfo.getAccessToken().equals(reflectedToken)) {
                    return false;
                }
            }
            
            return true;
            
        } catch (Exception e) {
            LOG.debug("State synchronization verification failed", e);
            return false;
        }
    }
    
    /**
     * Handle synchronization failures
     */
    private static void handleSyncFailure() {
        try {
            LOG.warn("Handling synchronization failure...");
            
            // Try to resolve conflicts
            resolveAuthConflict();
            
            // If still failing, try to restart the bypass system
            if (!verifyStateSynchronization()) {
                LOG.warn("Attempting to restart bypass system...");
                restartBypassSystem();
            }
            
        } catch (Exception e) {
            LOG.error("Failed to handle synchronization failure", e);
        }
    }
    
    /**
     * Restart the entire bypass system
     */
    private static void restartBypassSystem() {
        try {
            LOG.info("Restarting bypass system...");
            
            // Clear current state
            ZencoderCredentialInjector.clearInjectedCredentials();
            ZencoderServiceHijacker.restoreOriginalServices();
            
            // Wait a moment
            Thread.sleep(1000);
            
            // Restart with fresh credentials
            FakeUserDataBuilder.FakeUserProfile newProfile = FakeUserDataBuilder.buildCompleteProfile();
            
            if (ZencoderServiceHijacker.hijackAllServices() && 
                ZencoderCredentialInjector.injectFakeCredentials(newProfile)) {
                
                currentAuthInfo = newProfile.authInfo;
                currentUserData = newProfile.userData;
                currentTokenSet = newProfile.tokenSet;
                authBypassActive = true;
                
                LOG.info("Bypass system restarted successfully");
            } else {
                LOG.error("Failed to restart bypass system");
                authBypassActive = false;
            }
            
        } catch (Exception e) {
            LOG.error("Failed to restart bypass system", e);
            authBypassActive = false;
        }
    }
    
    /**
     * Schedule periodic synchronization
     */
    private static void schedulePeriodicSync() {
        syncAlarm.addRequest(() -> {
            if (monitoringActive.get()) {
                synchronizeAuthState();
                schedulePeriodicSync(); // Reschedule
            }
        }, SYNC_INTERVAL_MS);
    }
    
    /**
     * Schedule periodic conflict checking
     */
    private static void scheduleConflictChecking() {
        conflictAlarm.addRequest(() -> {
            if (monitoringActive.get()) {
                if (detectAuthConflict()) {
                    resolveAuthConflict();
                }
                lastConflictCheckTime.set(System.currentTimeMillis());
                scheduleConflictChecking(); // Reschedule
            }
        }, CONFLICT_CHECK_INTERVAL_MS);
    }
    
    /**
     * Get current bypass status
     */
    public static boolean isAuthBypassActive() {
        return authBypassActive;
    }
    
    /**
     * Get current authentication info
     */
    @Nullable
    public static AuthInfo getCurrentAuthInfo() {
        return currentAuthInfo;
    }
    
    /**
     * Get current user data
     */
    @Nullable
    public static UserData getCurrentUserData() {
        return currentUserData;
    }
    
    /**
     * Get current token set
     */
    @Nullable
    public static FakeTokenGenerator.FakeTokenSet getCurrentTokenSet() {
        return currentTokenSet;
    }
    
    /**
     * Check if monitoring is active
     */
    public static boolean isMonitoringActive() {
        return monitoringActive.get();
    }
    
    /**
     * Get last synchronization time
     */
    public static long getLastSyncTime() {
        return lastSyncTime.get();
    }
    
    /**
     * Get last conflict check time
     */
    public static long getLastConflictCheckTime() {
        return lastConflictCheckTime.get();
    }
    
    /**
     * Get synchronization status summary
     */
    @NotNull
    public static String getSyncStatus() {
        return String.format("SyncStatus{active=%s, bypass=%s, lastSync=%d, lastConflictCheck=%d}",
                monitoringActive.get(),
                authBypassActive,
                lastSyncTime.get(),
                lastConflictCheckTime.get());
    }
}