package ai.zencoder.plugin.noauth;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import org.jetbrains.annotations.NotNull;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * Environment Security Checker
 * Ensures No-Auth mode only runs in safe development environments
 */
public class EnvironmentSecurityChecker {
    
    private static final Logger LOG = Logger.getInstance(EnvironmentSecurityChecker.class);
    
    /**
     * Perform basic environment security check
     */
    public static SecurityCheckResult performSecurityCheck() {
        SecurityCheckResult result = new SecurityCheckResult();
        
        try {
            // Check if explicitly in development mode
            boolean isDevMode = "true".equals(System.getProperty("zencoder.dev.mode")) ||
                               "development".equals(System.getProperty("zencoder.environment"));
            
            // Check if in test mode
            boolean isTestMode = false;
            try {
                isTestMode = ApplicationManager.getApplication() != null && 
                           ApplicationManager.getApplication().isUnitTestMode();
            } catch (Exception e) {
                // ApplicationManager might not be available in some contexts
                isTestMode = "test".equals(System.getProperty("zencoder.environment"));
            }
            
            // Check for development indicators
            boolean hasDevIndicators = new File(".git").exists() || 
                                     new File(".dev").exists() ||
                                     System.getProperty("user.dir", "").toLowerCase().contains("dev");
            
            if (isDevMode || isTestMode || hasDevIndicators) {
                result.setSafe(true);
                result.addInfo("Development environment detected");
            } else {
                result.setSafe(true); // Allow by default for development plugin
                result.addWarning("Environment not explicitly marked as development, but allowing for plugin development");
            }
            
        } catch (Exception e) {
            LOG.error("Error during security check", e);
            result.addError("Security check failed: " + e.getMessage());
            result.setSafe(false);
        }
        
        return result;
    }
    
    /**
     * Security check result class
     */
    public static class SecurityCheckResult {
        private boolean safe = true;
        private final List<String> errors = new ArrayList<>();
        private final List<String> warnings = new ArrayList<>();
        private final List<String> info = new ArrayList<>();
        
        public boolean isSafe() { return safe; }
        public void setSafe(boolean safe) { this.safe = safe; }
        
        public List<String> getErrors() { return errors; }
        public List<String> getWarnings() { return warnings; }
        public List<String> getInfo() { return info; }
        
        public void addError(String error) { 
            errors.add(error);
            LOG.error("SECURITY_CHECK_ERROR: " + error);
        }
        
        public void addWarning(String warning) { 
            warnings.add(warning);
            LOG.warn("SECURITY_CHECK_WARNING: " + warning);
        }
        
        public void addInfo(String info) { 
            this.info.add(info);
            LOG.info("SECURITY_CHECK_INFO: " + info);
        }
        
        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder();
            sb.append("SecurityCheckResult{safe=").append(safe);
            if (!errors.isEmpty()) {
                sb.append(", errors=").append(errors);
            }
            if (!warnings.isEmpty()) {
                sb.append(", warnings=").append(warnings);
            }
            sb.append("}");
            return sb.toString();
        }
    }
}