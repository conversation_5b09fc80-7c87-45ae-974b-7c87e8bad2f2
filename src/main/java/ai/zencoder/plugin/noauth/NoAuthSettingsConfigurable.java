package ai.zencoder.plugin.noauth;

import ai.zencoder.plugin.auth.AuthService;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.options.Configurable;
import com.intellij.openapi.options.ConfigurationException;
import com.intellij.openapi.ui.VerticalFlowLayout;
import org.jetbrains.annotations.Nls;
import org.jetbrains.annotations.Nullable;

import javax.swing.*;
import java.awt.*;

/**
 * No-Auth插件的设置配置页面
 */
public class NoAuthSettingsConfigurable implements Configurable {
    
    private JPanel mainPanel;
    private JLabel statusLabel;
    private JTextArea infoArea;
    
    @Nls(capitalization = Nls.Capitalization.Title)
    @Override
    public String getDisplayName() {
        return "Zencoder No-Auth";
    }
    
    @Nullable
    @Override
    public JComponent createComponent() {
        mainPanel = new JPanel(new VerticalFlowLayout());
        
        // 标题
        JLabel titleLabel = new JLabel("Zencoder No-Auth Extension");
        titleLabel.setFont(titleLabel.getFont().deriveFont(Font.BOLD, 16f));
        mainPanel.add(titleLabel);
        
        // 状态显示
        statusLabel = new JLabel();
        statusLabel.setFont(statusLabel.getFont().deriveFont(Font.BOLD));
        mainPanel.add(statusLabel);
        
        // 信息区域
        infoArea = new JTextArea();
        infoArea.setEditable(false);
        infoArea.setOpaque(false);
        infoArea.setFont(infoArea.getFont().deriveFont(12f));
        infoArea.setRows(10);
        infoArea.setColumns(50);
        
        JScrollPane scrollPane = new JScrollPane(infoArea);
        scrollPane.setPreferredSize(new Dimension(500, 200));
        mainPanel.add(scrollPane);
        
        // 警告信息
        JLabel warningLabel = new JLabel("<html><b>Warning:</b> This extension is for development and testing only!</html>");
        warningLabel.setForeground(Color.RED);
        mainPanel.add(warningLabel);
        
        updateStatus();
        
        return mainPanel;
    }
    
    private void updateStatus() {
        try {
            AuthServiceReplacer replacer = ApplicationManager.getApplication().getService(AuthServiceReplacer.class);

            if (replacer != null && replacer.isNoAuthActive()) {
                AuthService authService = replacer.getAuthService();
                statusLabel.setText("Status: No-Auth Mode ACTIVE");
                statusLabel.setForeground(Color.GREEN);

                infoArea.setText(
                    "No-Auth mode is currently active.\n\n" +
                    "Features:\n" +
                    "• Authentication is bypassed\n" +
                    "• Mock user credentials are provided\n" +
                    "• All Zencoder features are available\n" +
                    "• Suitable for offline development\n\n" +
                    "Mock User Information:\n" +
                    "• User ID: mock_user_id\n" +
                    "• Name: Mock User\n" +
                    "• Email: <EMAIL>\n" +
                    "• Plan: Free\n\n" +
                    "Technical Details:\n" +
                    "• AuthService Implementation: " + replacer.getAuthServiceClassName() + "\n" +
                    "• Access Token: " + (authService.a() != null ? "Available" : "Not Available") + "\n" +
                    "• Authentication Status: " + (authService.isAuthenticated() ? "Authenticated" : "Not Authenticated")
                );
            } else {
                statusLabel.setText("Status: No-Auth Mode INACTIVE");
                statusLabel.setForeground(Color.RED);

                String authServiceName = replacer != null ? replacer.getAuthServiceClassName() : "Unknown";
                infoArea.setText(
                    "No-Auth mode is currently inactive.\n\n" +
                    "Current Configuration:\n" +
                    "• AuthService Implementation: " + authServiceName + "\n" +
                    "• AuthServiceReplacer Available: " + (replacer != null ? "Yes" : "No") + "\n\n" +
                    "To activate No-Auth mode:\n" +
                    "1. Ensure the No-Auth plugin is properly installed\n" +
                    "2. Check that the plugin.xml configuration is correct\n" +
                    "3. Restart the IDE if necessary\n\n" +
                    "If you're seeing this message and expect No-Auth mode to be active,\n" +
                    "please check the plugin installation and configuration."
                );
            }
            
        } catch (Exception e) {
            statusLabel.setText("Status: ERROR");
            statusLabel.setForeground(Color.RED);
            infoArea.setText("Error checking No-Auth status: " + e.getMessage());
        }
    }
    
    @Override
    public boolean isModified() {
        return false; // 这个配置页面是只读的
    }
    
    @Override
    public void apply() throws ConfigurationException {
        // 不需要应用任何设置，这是一个只读的信息页面
    }
    
    @Override
    public void reset() {
        updateStatus(); // 刷新状态显示
    }
}
