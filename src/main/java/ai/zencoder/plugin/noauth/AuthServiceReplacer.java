package ai.zencoder.plugin.noauth;

import ai.zencoder.plugin.api.NoAuthService;
import ai.zencoder.plugin.api.SecureNoAuthService;
import ai.zencoder.plugin.auth.AuthService;
import com.intellij.openapi.components.Service;
import com.intellij.openapi.diagnostic.Logger;
import org.jetbrains.annotations.NotNull;

/**
 * AuthService 替换器
 * 确保 NoAuthService 能够正确替换原有的 AuthService 实现
 */
@Service
public final class AuthServiceReplacer {
    
    private static final Logger LOG = Logger.getInstance(AuthServiceReplacer.class);
    
    private volatile AuthService authService;
    private volatile boolean initialized = false;
    
    /**
     * 获取 AuthService 实例
     * 如果尚未初始化，则进行初始化
     */
    @NotNull
    public AuthService getAuthService() {
        if (!initialized) {
            synchronized (this) {
                if (!initialized) {
                    initializeAuthService();
                    initialized = true;
                }
            }
        }
        return authService;
    }
    
    /**
     * 初始化 AuthService
     * 优先使用 SecureNoAuthService，如果不可用则使用 NoAuthService
     */
    private void initializeAuthService() {
        try {
            // 尝试创建 SecureNoAuthService 实例
            authService = new SecureNoAuthService();
            LOG.info("Successfully initialized SecureNoAuthService");

        } catch (Exception e) {
            LOG.error("Failed to initialize SecureNoAuthService", e);

            // 如果 SecureNoAuthService 初始化失败，尝试使用 NoAuthService
            try {
                authService = new NoAuthService();
                LOG.warn("Fallback to NoAuthService");
            } catch (Exception fallbackException) {
                LOG.error("Failed to initialize NoAuthService", fallbackException);

                // 最后尝试获取系统默认的 AuthService（但不直接使用以避免冲突）
                try {
                    // 避免循环依赖，直接创建 NoAuthService
                    authService = new NoAuthService();
                    LOG.warn("Created emergency NoAuthService instance");
                } catch (Exception emergencyException) {
                    LOG.error("All fallback options failed", emergencyException);
                    authService = new NoAuthService();
                    LOG.warn("Created final emergency NoAuthService instance");
                }
            }
        }
    }
    
    /**
     * 检查当前是否使用 NoAuthService 或 SecureNoAuthService
     */
    public boolean isNoAuthActive() {
        AuthService service = getAuthService();
        return service instanceof NoAuthService || service instanceof SecureNoAuthService;
    }
    
    /**
     * 强制重新初始化 AuthService
     */
    public void reinitialize() {
        synchronized (this) {
            initialized = false;
            authService = null;
        }
        // 触发重新初始化
        getAuthService();
    }
    
    /**
     * 获取当前 AuthService 的类名
     */
    @NotNull
    public String getAuthServiceClassName() {
        return getAuthService().getClass().getName();
    }
}
