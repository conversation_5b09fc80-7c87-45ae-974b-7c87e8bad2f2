package ai.zencoder.plugin.noauth;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.components.PersistentStateComponent;
import com.intellij.openapi.components.Service;
import com.intellij.openapi.components.State;
import com.intellij.openapi.components.Storage;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.util.xmlb.XmlSerializerUtil;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 安全的No-Auth配置管理器
 * 管理No-Auth模式的安全配置和审计日志
 */
@Service
@State(
    name = "SecureNoAuthConfig",
    storages = @Storage("secure-noauth-config.xml")
)
public final class SecureNoAuthConfigManager implements PersistentStateComponent<SecureNoAuthConfigManager.State> {
    
    private static final Logger LOG = Logger.getInstance(SecureNoAuthConfigManager.class);
    
    private State state = new State();
    
    public static class State {
        // 基本配置
        public boolean enabled = false;
        public String permissionLevel = "LIMITED_WRITE";
        public int maxOperationsPerSession = 1000;
        public int sessionTimeoutHours = 24;
        
        // 安全配置
        public boolean requireExplicitDevMode = true;
        public boolean logAllOperations = true;
        public boolean showSecurityWarnings = true;
        public List<String> allowedEnvironments = new ArrayList<>();
        
        // 审计信息
        public List<String> auditLog = new ArrayList<>();
        public String lastActivation = "";
        public String lastDeactivation = "";
        public int totalSessions = 0;
        public int totalOperations = 0;
        
        // 绕过系统专用字段
        public String fakeUserId = "bypass-user-001";
        public String fakeUserEmail = "<EMAIL>";
        public String fakeUserName = "Zencoder Bypass User";
        
        public State() {
            // 默认允许的环境
            allowedEnvironments.add("development");
            allowedEnvironments.add("test");
            allowedEnvironments.add("local");
        }
    }
    
    @Override
    public @Nullable State getState() {
        return state;
    }
    
    @Override
    public void loadState(@NotNull State state) {
        XmlSerializerUtil.copyBean(state, this.state);
    }
    
    /**
     * 检查是否启用No-Auth模式
     */
    public boolean isEnabled() {
        return state.enabled;
    }
    
    /**
     * 启用No-Auth模式（需要安全检查）
     */
    public boolean enableNoAuthMode(@NotNull String reason) {
        // 执行安全检查
        EnvironmentSecurityChecker.SecurityCheckResult securityCheck = 
            EnvironmentSecurityChecker.performSecurityCheck();
        
        if (!securityCheck.isSafe()) {
            LOG.error("Cannot enable No-Auth mode: environment security check failed");
            addAuditEntry("ENABLE_FAILED", "Security check failed: " + securityCheck.getErrors());
            return false;
        }
        
        if (state.requireExplicitDevMode && !isExplicitDevModeEnabled()) {
            LOG.error("Cannot enable No-Auth mode: explicit dev mode required but not set");
            addAuditEntry("ENABLE_FAILED", "Explicit dev mode required but not enabled");
            return false;
        }
        
        state.enabled = true;
        state.lastActivation = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        state.totalSessions++;
        
        addAuditEntry("ENABLED", reason);
        LOG.warn("No-Auth mode ENABLED - Reason: " + reason);
        
        return true;
    }
    
    /**
     * 禁用No-Auth模式
     */
    public void disableNoAuthMode(@NotNull String reason) {
        state.enabled = false;
        state.lastDeactivation = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        
        addAuditEntry("DISABLED", reason);
        LOG.info("No-Auth mode DISABLED - Reason: " + reason);
    }
    
    /**
     * 获取权限级别
     */
    public String getPermissionLevel() {
        return state.permissionLevel != null ? state.permissionLevel : "LIMITED_WRITE";
    }
    
    /**
     * 设置权限级别
     */
    public void setPermissionLevel(@NotNull String level) {
        String oldLevel = state.permissionLevel;
        state.permissionLevel = level;
        
        addAuditEntry("PERMISSION_CHANGED", "From " + oldLevel + " to " + level);
        LOG.info("Permission level changed from " + oldLevel + " to " + level);
    }
    
    /**
     * 记录操作
     */
    public void recordOperation(@NotNull String operation, @NotNull String details) {
        state.totalOperations++;
        
        if (state.logAllOperations) {
            addAuditEntry("OPERATION", operation + " - " + details);
        }
    }
    
    /**
     * 添加审计日志条目
     */
    private void addAuditEntry(@NotNull String action, @NotNull String details) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        String entry = String.format("[%s] %s: %s", timestamp, action, details);
        
        state.auditLog.add(entry);
        
        // 限制审计日志大小
        if (state.auditLog.size() > 1000) {
            state.auditLog.remove(0);
        }
        
        LOG.info("AUDIT: " + entry);
    }
    
    /**
     * 检查是否明确启用了开发模式
     */
    private boolean isExplicitDevModeEnabled() {
        return "true".equals(System.getProperty("zencoder.dev.mode")) ||
               "development".equals(System.getProperty("zencoder.environment"));
    }
    
    /**
     * 获取配置摘要
     */
    public String getConfigSummary() {
        return String.format(
            "SecureNoAuthConfig{enabled=%s, permissionLevel=%s, totalSessions=%d, totalOperations=%d, lastActivation=%s}",
            state.enabled, state.permissionLevel, state.totalSessions, state.totalOperations, state.lastActivation
        );
    }
    
    /**
     * 获取审计日志
     */
    public List<String> getAuditLog() {
        return new ArrayList<>(state.auditLog);
    }
    
    /**
     * 清除审计日志（需要确认）
     */
    public void clearAuditLog(@NotNull String reason) {
        int logSize = state.auditLog.size();
        state.auditLog.clear();
        
        addAuditEntry("AUDIT_CLEARED", "Cleared " + logSize + " entries - Reason: " + reason);
        LOG.warn("Audit log cleared - " + logSize + " entries removed - Reason: " + reason);
    }
    
    /**
     * 验证配置
     */
    public List<String> validateConfiguration() {
        List<String> issues = new ArrayList<>();
        
        if (state.maxOperationsPerSession <= 0) {
            issues.add("Max operations per session must be positive");
        }
        
        if (state.sessionTimeoutHours <= 0 || state.sessionTimeoutHours > 168) {
            issues.add("Session timeout must be between 1 and 168 hours");
        }
        
        if (state.allowedEnvironments.isEmpty()) {
            issues.add("At least one allowed environment must be specified");
        }
        
        if (state.permissionLevel == null || state.permissionLevel.trim().isEmpty()) {
            issues.add("Permission level cannot be empty");
        }
        
        return issues;
    }
    
    /**
     * 重置配置到默认值
     */
    public void resetToDefaults(@NotNull String reason) {
        State oldState = new State();
        XmlSerializerUtil.copyBean(state, oldState);
        
        state = new State();
        
        addAuditEntry("CONFIG_RESET", "Reset to defaults - Reason: " + reason);
        LOG.warn("Configuration reset to defaults - Reason: " + reason);
    }
    
    // ===== 绕过系统专用方法 =====
    
    /**
     * 获取单例实例
     */
    public static SecureNoAuthConfigManager getInstance() {
        return ApplicationManager.getApplication().getService(SecureNoAuthConfigManager.class);
    }
    
    /**
     * 检查绕过是否启用
     */
    public boolean isBypassEnabled() {
        return state.enabled;
    }
    
    /**
     * 设置绕过启用状态
     */
    public void setBypassEnabled(boolean enabled) {
        state.enabled = enabled;
        if (enabled) {
            state.lastActivation = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        } else {
            state.lastDeactivation = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        }
    }
    
    /**
     * 设置最后绕过时间
     */
    public void setLastBypassTime(long timestamp) {
        state.lastActivation = LocalDateTime.ofInstant(
            java.time.Instant.ofEpochMilli(timestamp), 
            java.time.ZoneId.systemDefault()
        ).format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
    }
    
    /**
     * 设置假用户ID
     */
    public void setFakeUserId(@NotNull String userId) {
        state.fakeUserId = userId;
    }
    
    /**
     * 获取假用户ID
     */
    public String getFakeUserId() {
        return state.fakeUserId;
    }
    
    /**
     * 设置假用户邮箱
     */
    public void setFakeUserEmail(@NotNull String email) {
        state.fakeUserEmail = email;
    }
    
    /**
     * 获取假用户邮箱
     */
    public String getFakeUserEmail() {
        return state.fakeUserEmail;
    }
    
    /**
     * 设置假用户名
     */
    public void setFakeUserName(@NotNull String name) {
        state.fakeUserName = name;
    }
    
    /**
     * 获取假用户名
     */
    public String getFakeUserName() {
        return state.fakeUserName;
    }
}