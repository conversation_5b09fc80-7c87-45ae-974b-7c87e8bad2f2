package ai.zencoder.plugin.auth;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * AuthInfo类的单元测试
 */
public class AuthInfoTest {
    
    @Test
    public void testAuthInfoCreation() {
        String accessToken = "test_access_token";
        String refreshToken = "test_refresh_token";
        
        AuthInfo authInfo = new AuthInfo(accessToken, refreshToken);
        
        assertEquals(accessToken, authInfo.getAccessToken());
        assertEquals(refreshToken, authInfo.getRefreshToken());
        assertFalse(authInfo.isExpired());
    }
    
    @Test
    public void testAuthInfoWithNullRefreshToken() {
        String accessToken = "test_access_token";
        
        AuthInfo authInfo = new AuthInfo(accessToken, null);
        
        assertEquals(accessToken, authInfo.getAccessToken());
        assertNull(authInfo.getRefreshToken());
        assertFalse(authInfo.isExpired());
    }
    
    @Test
    public void testAuthInfoExpiration() {
        String accessToken = "test_access_token";
        String refreshToken = "test_refresh_token";
        long pastTime = System.currentTimeMillis() - 1000; // 1秒前过期
        
        AuthInfo authInfo = new AuthInfo(accessToken, refreshToken, pastTime);
        
        assertTrue(authInfo.isExpired());
    }
    
    @Test
    public void testAuthInfoExpiringSoon() {
        String accessToken = "test_access_token";
        String refreshToken = "test_refresh_token";
        long soonTime = System.currentTimeMillis() + 60000; // 1分钟后过期
        
        AuthInfo authInfo = new AuthInfo(accessToken, refreshToken, soonTime);
        
        assertTrue(authInfo.isExpiringSoon());
        assertFalse(authInfo.isExpired());
    }
    
    @Test
    public void testAuthInfoEquality() {
        String accessToken = "test_access_token";
        String refreshToken = "test_refresh_token";
        long expiresAt = System.currentTimeMillis() + 3600000;
        
        AuthInfo authInfo1 = new AuthInfo(accessToken, refreshToken, expiresAt);
        AuthInfo authInfo2 = new AuthInfo(accessToken, refreshToken, expiresAt);
        
        assertEquals(authInfo1, authInfo2);
        assertEquals(authInfo1.hashCode(), authInfo2.hashCode());
    }
    
    @Test
    public void testAuthInfoToString() {
        String accessToken = "test_access_token_very_long";
        String refreshToken = "test_refresh_token";
        
        AuthInfo authInfo = new AuthInfo(accessToken, refreshToken);
        String toString = authInfo.toString();
        
        assertTrue(toString.contains("AuthInfo"));
        assertTrue(toString.contains("test_access..."));
        assertTrue(toString.contains("present"));
    }
    
    @Test(expected = NullPointerException.class)
    public void testAuthInfoWithNullAccessToken() {
        new AuthInfo(null, "refresh_token");
    }
}
