package ai.zencoder.plugin.auth;

import org.junit.Test;
import java.util.*;
import static org.junit.Assert.*;

/**
 * UserData类的单元测试
 */
public class UserDataTest {
    
    @Test
    public void testUserDataCreation() {
        String userId = "test_user_id";
        String displayName = "Test User";
        String email = "<EMAIL>";
        List<String> plans = Arrays.asList("Free", "Pro");
        List<String> permissions = Arrays.asList("read", "write");
        Map<String, Object> customClaims = new HashMap<>();
        customClaims.put("role", "developer");
        
        UserData userData = new UserData(userId, displayName, email, plans, permissions, customClaims);
        
        assertEquals(userId, userData.getUserId());
        assertEquals(displayName, userData.getDisplayName());
        assertEquals(email, userData.getEmail());
        assertEquals(plans, userData.getPlans());
        assertEquals(permissions, userData.getPermissions());
        assertEquals(customClaims, userData.getCustomClaims());
    }
    
    @Test
    public void testUserDataWithNullCustomClaims() {
        String userId = "test_user_id";
        String displayName = "Test User";
        String email = "<EMAIL>";
        List<String> plans = Arrays.asList("Free");
        List<String> permissions = Arrays.asList("read");
        
        UserData userData = new UserData(userId, displayName, email, plans, permissions, null);
        
        assertNull(userData.getCustomClaims());
        assertNull(userData.getCustomClaim("any_key"));
    }
    
    @Test
    public void testHasPlan() {
        List<String> plans = Arrays.asList("Free", "Pro");
        UserData userData = createTestUserData(plans, Collections.emptyList());
        
        assertTrue(userData.hasPlan("Free"));
        assertTrue(userData.hasPlan("Pro"));
        assertFalse(userData.hasPlan("Enterprise"));
    }
    
    @Test
    public void testHasPermission() {
        List<String> permissions = Arrays.asList("read", "write", "admin");
        UserData userData = createTestUserData(Collections.singletonList("Free"), permissions);
        
        assertTrue(userData.hasPermission("read"));
        assertTrue(userData.hasPermission("write"));
        assertTrue(userData.hasPermission("admin"));
        assertFalse(userData.hasPermission("delete"));
    }
    
    @Test
    public void testIsVipWithFreePlan() {
        List<String> plans = Arrays.asList("Free");
        UserData userData = createTestUserData(plans, Collections.emptyList());
        
        assertFalse(userData.isVip());
    }
    
    @Test
    public void testIsVipWithTrialPlan() {
        List<String> plans = Arrays.asList("Trial");
        UserData userData = createTestUserData(plans, Collections.emptyList());
        
        assertFalse(userData.isVip());
    }
    
    @Test
    public void testIsVipWithPaidPlan() {
        List<String> plans = Arrays.asList("Pro");
        UserData userData = createTestUserData(plans, Collections.emptyList());
        
        assertTrue(userData.isVip());
    }
    
    @Test
    public void testIsVipWithMixedPlans() {
        List<String> plans = Arrays.asList("Free", "Pro");
        UserData userData = createTestUserData(plans, Collections.emptyList());
        
        assertTrue(userData.isVip());
    }
    
    @Test
    public void testGetCustomClaim() {
        Map<String, Object> customClaims = new HashMap<>();
        customClaims.put("role", "developer");
        customClaims.put("level", 5);
        
        UserData userData = new UserData("id", "name", "email", 
            Collections.emptyList(), Collections.emptyList(), customClaims);
        
        assertEquals("developer", userData.getCustomClaim("role"));
        assertEquals(5, userData.getCustomClaim("level"));
        assertNull(userData.getCustomClaim("nonexistent"));
    }
    
    @Test
    public void testUserDataEquality() {
        UserData userData1 = createTestUserData(Arrays.asList("Free"), Arrays.asList("read"));
        UserData userData2 = createTestUserData(Arrays.asList("Free"), Arrays.asList("read"));
        
        assertEquals(userData1, userData2);
        assertEquals(userData1.hashCode(), userData2.hashCode());
    }
    
    @Test
    public void testUserDataToString() {
        UserData userData = createTestUserData(Arrays.asList("Free"), Arrays.asList("read"));
        String toString = userData.toString();
        
        assertTrue(toString.contains("UserData"));
        assertTrue(toString.contains("test_user_id"));
        assertTrue(toString.contains("Test User"));
        assertTrue(toString.contains("<EMAIL>"));
    }
    
    @Test(expected = NullPointerException.class)
    public void testUserDataWithNullUserId() {
        new UserData(null, "name", "email", 
            Collections.emptyList(), Collections.emptyList(), null);
    }
    
    @Test(expected = NullPointerException.class)
    public void testUserDataWithNullDisplayName() {
        new UserData("id", null, "email", 
            Collections.emptyList(), Collections.emptyList(), null);
    }
    
    @Test(expected = NullPointerException.class)
    public void testUserDataWithNullEmail() {
        new UserData("id", "name", null, 
            Collections.emptyList(), Collections.emptyList(), null);
    }
    
    @Test(expected = NullPointerException.class)
    public void testUserDataWithNullPlans() {
        new UserData("id", "name", "email", 
            null, Collections.emptyList(), null);
    }
    
    @Test(expected = NullPointerException.class)
    public void testUserDataWithNullPermissions() {
        new UserData("id", "name", "email", 
            Collections.emptyList(), null, null);
    }
    
    private UserData createTestUserData(List<String> plans, List<String> permissions) {
        return new UserData("test_user_id", "Test User", "<EMAIL>", 
            plans, permissions, null);
    }
}
